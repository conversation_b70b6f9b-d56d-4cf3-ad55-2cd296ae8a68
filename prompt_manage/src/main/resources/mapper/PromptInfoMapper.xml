<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.prompt_manage.repository.PromptInfoMapper">

    <!-- 分页查询提示词注册表视图 -->
    <select id="selectPromptRegistryPage" resultType="com.chinaunicom.prompt_manage.model.vo.PromptRegistryVO">
        SELECT
            pv.id,
            pi.name,
            pv.version,
            pi.project_id as projectId,
            pi.description,
            pi.category,
            pi.tags,
            pi.created_by as createdBy,
            pi.create_time as createTime,
            COALESCE(pv.created_by, pi.update_by) as updateBy,
            GREATEST(pi.update_time, pv.create_time) as updateTime,
            pv.content,
            pv.version_note as versionNote,
            pv.status,
            pi.current_version as currentVersion,
            pi.is_deleted as isDeleted
        FROM prompt_info pi
        JOIN prompt_version pv ON pi.id = pv.prompt_id AND pi.current_version = pv.version
        WHERE pi.is_deleted = 0 AND pv.is_deleted = 0
        <if test="projectId != null and projectId != ''">
            AND pi.project_id = #{projectId}
        </if>
        <if test="name != null and name != ''">
            AND pi.name LIKE CONCAT('%', #{name}, '%')
        </if>
        ORDER BY pi.update_time DESC
    </select>

    <!-- 根据ID查询提示词注册表详情 -->
    <select id="selectPromptRegistryById" resultType="com.chinaunicom.prompt_manage.model.vo.PromptRegistryVO">
        SELECT
            pv.id,
            pi.name,
            pv.version,
            pi.project_id as projectId,
            pi.description,
            pi.category,
            pi.tags,
            pi.created_by as createdBy,
            pi.create_time as createTime,
            COALESCE(pv.created_by, pi.update_by) as updateBy,
            GREATEST(pi.update_time, pv.create_time) as updateTime,
            pv.content,
            pv.version_note as versionNote,
            pv.status,
            pi.current_version as currentVersion,
            pi.is_deleted as isDeleted
        FROM prompt_info pi
        JOIN prompt_version pv ON pi.id = pv.prompt_id AND pi.current_version = pv.version
        WHERE pi.id = #{id} AND pi.is_deleted = 0 AND pv.is_deleted = 0
    </select>

    <!-- 根据项目ID、名称和版本查询提示词 -->
    <select id="selectPromptRegistryByProjectAndNameAndVersion" resultType="com.chinaunicom.prompt_manage.model.vo.PromptRegistryVO">
        SELECT
            pv.id,
            pi.name,
            pv.version,
            pi.project_id as projectId,
            pi.description,
            pi.category,
            pi.tags,
            pi.created_by as createdBy,
            pi.create_time as createTime,
            COALESCE(pv.created_by, pi.update_by) as updateBy,
            GREATEST(pi.update_time, pv.create_time) as updateTime,
            pv.content,
            pv.version_note as versionNote,
            pv.status,
            pi.current_version as currentVersion,
            pi.is_deleted as isDeleted
        FROM prompt_info pi
        JOIN prompt_version pv ON pi.id = pv.prompt_id
        WHERE pi.project_id = #{projectId} 
          AND pi.name = #{name} 
          AND pv.version = #{version}
          AND pi.is_deleted = 0 
          AND pv.is_deleted = 0
    </select>

    <!-- 获取下一个版本号 -->
    <select id="selectNextVersion" resultType="java.lang.Long">
        SELECT COALESCE(MAX(pi.current_version), 0) + 1
        FROM prompt_info pi
        WHERE pi.project_id = #{projectId} 
          AND pi.name = #{name}
          AND pi.is_deleted = 0
    </select>

</mapper>
