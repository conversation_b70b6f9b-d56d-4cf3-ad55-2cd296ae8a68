<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.prompt_manage.repository.HumanEvaluationMapper">

    <!-- 分页查询人工评估列表 -->
    <select id="selectHumanEvaluationPage" resultType="com.chinaunicom.prompt_manage.model.vo.HumanEvaluationVO">
        SELECT
            he.id,
            he.prompt_id as promptId,
            pi.name as promptName,
            he.prompt_version as promptVersion,
            he.project_id as projectId,
            he.test_set_id as testSetId,
            pts.name as testSetName,
            he.created_by as createdBy,
            he.create_time as createTime,
            he.avg_score as avgScore,
            (SELECT COUNT(*) FROM prompt_human_evaluation_detail hed WHERE hed.evaluation_id = he.id) as detailCount
        FROM prompt_human_evaluation he
        LEFT JOIN prompt_info pi ON he.prompt_id = pi.id AND pi.is_deleted = 0
        LEFT JOIN prompt_test_set pts ON he.test_set_id = pts.id AND pts.is_deleted = 0
        WHERE 1=1
        <if test="projectId != null and projectId != ''">
            AND he.project_id = #{projectId}
        </if>
        <if test="promptId != null">
            AND he.prompt_id = #{promptId}
        </if>
        <if test="promptName != null and promptName != ''">
            AND pi.name LIKE CONCAT('%', #{promptName}, '%')
        </if>
        <if test="testSetId != null">
            AND he.test_set_id = #{testSetId}
        </if>
        <if test="testSetName != null and testSetName != ''">
            AND pts.name LIKE CONCAT('%', #{testSetName}, '%')
        </if>
        <if test="createdBy != null and createdBy != ''">
            AND he.created_by = #{createdBy}
        </if>
        <if test="minAvgScore != null">
            AND he.avg_score >= #{minAvgScore}
        </if>
        <if test="maxAvgScore != null">
            AND he.avg_score &lt;= #{maxAvgScore}
        </if>
        ORDER BY he.create_time DESC
    </select>

    <!-- 根据ID查询人工评估详情（不包含详情列表） -->
    <select id="selectHumanEvaluationById" resultType="com.chinaunicom.prompt_manage.model.vo.HumanEvaluationVO">
        SELECT
            he.id,
            he.prompt_id as promptId,
            pi.name as promptName,
            he.prompt_version as promptVersion,
            he.project_id as projectId,
            he.test_set_id as testSetId,
            pts.name as testSetName,
            he.created_by as createdBy,
            he.create_time as createTime,
            he.avg_score as avgScore,
            (SELECT COUNT(*) FROM prompt_human_evaluation_detail hed WHERE hed.evaluation_id = he.id) as detailCount
        FROM prompt_human_evaluation he
        LEFT JOIN prompt_info pi ON he.prompt_id = pi.id AND pi.is_deleted = 0
        LEFT JOIN prompt_test_set pts ON he.test_set_id = pts.id AND pts.is_deleted = 0
        WHERE he.id = #{id}
    </select>

</mapper>
