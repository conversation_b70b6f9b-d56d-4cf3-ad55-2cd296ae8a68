<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.prompt_manage.repository.PromptTestSetMapper">

    <!-- 分页查询测试集（不包含数据） -->
    <select id="selectTestSetPageWithoutData" resultType="com.chinaunicom.prompt_manage.model.vo.PromptTestSetVO">
        SELECT
            id,
            name,
            project_id as projectId,
            created_by as createdBy,
            create_time as createTime,
            update_by as updateBy,
            update_time as updateTime,
            NULL as csvData,
            is_deleted as isDeleted
        FROM prompt_test_set
        WHERE is_deleted = 0
        <if test="projectId != null and projectId != ''">
            AND project_id = #{projectId}
        </if>
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        ORDER BY update_time DESC
    </select>

    <!-- 根据ID查询测试集详情（包含数据） -->
    <select id="selectTestSetById" resultType="com.chinaunicom.prompt_manage.model.vo.PromptTestSetVO">
        SELECT
            id,
            name,
            project_id as projectId,
            created_by as createdBy,
            create_time as createTime,
            update_by as updateBy,
            update_time as updateTime,
            csv_data as csvData,
            is_deleted as isDeleted
        FROM prompt_test_set
        WHERE id = #{id} AND is_deleted = 0
    </select>

</mapper>
