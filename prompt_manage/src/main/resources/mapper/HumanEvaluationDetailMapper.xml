<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.prompt_manage.repository.HumanEvaluationDetailMapper">

    <!-- 根据评估ID查询评估详情列表 -->
    <select id="selectDetailsByEvaluationId" resultType="com.chinaunicom.prompt_manage.model.vo.HumanEvaluationVO$HumanEvaluationDetailVO">
        SELECT
            id,
            input_data as inputData,
            output_data as outputData,
            expect_output as expectOutput,
            note,
            score,
            create_time as createTime,
            update_time as updateTime
        FROM prompt_human_evaluation_detail
        WHERE evaluation_id = #{evaluationId}
        ORDER BY create_time ASC
    </select>

</mapper>
