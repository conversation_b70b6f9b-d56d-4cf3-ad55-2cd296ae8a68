server:
  port: 8082
  servlet:
    context-path: /prompt-manage

spring:
  application:
    name: prompt-manage
  
  # 数据源配置 - 使用Apollo配置中心管理
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          url: ${spring.datasource.url:**************************************************************************************************************************}
          username: ${spring.datasource.username:root}
          password: ${spring.datasource.password:password}
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            minimum-idle: 5
            maximum-pool-size: 20
            idle-timeout: 600000
            max-lifetime: 1800000
            connection-timeout: 30000

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.chinaunicom.prompt_manage.model.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: isDeleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto
    banner: false

# Apollo配置中心
apollo:
  meta: ${apollo.meta:http://localhost:8080}
  bootstrap:
    enabled: true
    namespaces: application,database,langchain4j

# 日志配置
logging:
  level:
    com.chinaunicom.prompt_manage: DEBUG
    com.baomidou.mybatisplus: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# LangChain4j配置 - 通过Apollo配置中心管理
langchain4j:
  open-ai:
    chat-model:
      api-key: ${langchain4j.openai.api-key:your-api-key}
      base-url: ${langchain4j.openai.base-url:https://api.openai.com/v1}
      model-name: ${langchain4j.openai.model-name:gpt-3.5-turbo}
      timeout: ${langchain4j.openai.timeout:600s}
