-- 提示词管理系统数据库初始化脚本
-- 创建时间: 2025-01-01
-- 版本: v1.0.0

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `prompt_manage` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `prompt_manage`;

-- 1. 提示词基本信息表
DROP TABLE IF EXISTS `prompt_info`;
CREATE TABLE `prompt_info` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `name` VARCHAR(64) NOT NULL COMMENT '提示词名称',
  `project_id` VARCHAR(64) NOT NULL COMMENT '项目ID',
  `description` VARCHAR(500) DEFAULT NULL COMMENT '提示词描述',
  `category` VARCHAR(32) DEFAULT NULL COMMENT '分类',
  `tags` VARCHAR(200) DEFAULT NULL COMMENT '标签，逗号分隔',
  `created_by` VARCHAR(32) DEFAULT NULL COMMENT '创建人，关联user.id',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` VARCHAR(32) DEFAULT NULL COMMENT '修改人，关联user.id',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
  `current_version` BIGINT DEFAULT 1 COMMENT '当前版本号',
  `is_deleted` INT DEFAULT 0 COMMENT '是否删除（0-未删除，1-已删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_prompt` (`project_id`, `name`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_category` (`category`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词基本信息表';

-- 2. 提示词版本信息表
DROP TABLE IF EXISTS `prompt_version`;
CREATE TABLE `prompt_version` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `prompt_id` BIGINT NOT NULL COMMENT '关联prompt_info.id',
  `version` BIGINT NOT NULL COMMENT '版本号',
  `content` TEXT NOT NULL COMMENT 'prompt内容，JSON格式',
  `version_note` VARCHAR(500) DEFAULT NULL COMMENT '版本说明',
  `status` VARCHAR(20) DEFAULT 'draft' COMMENT '状态：draft/published/archived',
  `created_by` VARCHAR(32) DEFAULT NULL COMMENT '版本创建人，关联user.id',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '版本创建时间',
  `is_deleted` INT DEFAULT 0 COMMENT '是否删除（0-未删除，1-已删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_prompt_version` (`prompt_id`, `version`),
  KEY `idx_prompt_id` (`prompt_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_version` (`version`),
  CONSTRAINT `fk_prompt_version_prompt_id` FOREIGN KEY (`prompt_id`) REFERENCES `prompt_info` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词版本信息表';

-- 3. 测试集管理表
DROP TABLE IF EXISTS `prompt_test_set`;
CREATE TABLE `prompt_test_set` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `name` VARCHAR(64) NOT NULL COMMENT '测试集名称',
  `project_id` VARCHAR(64) NOT NULL COMMENT '项目ID',
  `created_by` VARCHAR(32) DEFAULT NULL COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` VARCHAR(32) DEFAULT NULL COMMENT '修改人，关联user.id',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `csv_data` TEXT NOT NULL COMMENT '测试集可变json数据',
  `is_deleted` INT DEFAULT 0 COMMENT '是否删除（0-未删除，1-已删除）',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_name` (`name`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='测试集管理表';

-- 4. 提示词执行日志表
DROP TABLE IF EXISTS `prompt_execution_log`;
CREATE TABLE `prompt_execution_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `prompt_id` BIGINT DEFAULT NULL COMMENT '关联prompt_info.id或prompt_registry.id',
  `project_id` VARCHAR(64) NOT NULL COMMENT '项目ID',
  `input_data` TEXT DEFAULT NULL COMMENT '输入内容',
  `output_data` TEXT DEFAULT NULL COMMENT '模型输出',
  `status` VARCHAR(20) DEFAULT 'running' COMMENT '执行状态：success/fail/running',
  `executed_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
  `executed_by` VARCHAR(32) DEFAULT NULL COMMENT '执行人，关联user.id',
  PRIMARY KEY (`id`),
  KEY `idx_prompt_id` (`prompt_id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_status` (`status`),
  KEY `idx_executed_time` (`executed_time`),
  KEY `idx_executed_by` (`executed_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词执行日志表';

-- 5. 人工评估主表
DROP TABLE IF EXISTS `prompt_human_evaluation`;
CREATE TABLE `prompt_human_evaluation` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `prompt_id` BIGINT NOT NULL COMMENT '关联prompt_info.id',
  `prompt_version` VARCHAR(50) DEFAULT NULL COMMENT '快照版本（冗余存储）',
  `project_id` VARCHAR(64) NOT NULL COMMENT '项目ID',
  `test_set_id` BIGINT NOT NULL COMMENT '关联prompt_test_set.id',
  `created_by` VARCHAR(32) DEFAULT NULL COMMENT '评估人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `avg_score` DECIMAL(5,2) DEFAULT 0.00 COMMENT '平均得分',
  PRIMARY KEY (`id`),
  KEY `idx_prompt_id` (`prompt_id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_test_set_id` (`test_set_id`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='人工评估主表';

-- 6. 人工评估子条目表
DROP TABLE IF EXISTS `prompt_human_evaluation_detail`;
CREATE TABLE `prompt_human_evaluation_detail` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `evaluation_id` BIGINT NOT NULL COMMENT '关联prompt_human_evaluation.id',
  `input_data` TEXT NOT NULL COMMENT '测试输入',
  `output_data` TEXT NOT NULL COMMENT '模型输出',
  `expect_output` TEXT DEFAULT NULL COMMENT '期望答案',
  `note` VARCHAR(64) DEFAULT NULL COMMENT '备注',
  `score` DECIMAL(5,2) NOT NULL COMMENT '评分',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_evaluation_id` (`evaluation_id`),
  KEY `idx_score` (`score`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_evaluation_detail_evaluation_id` FOREIGN KEY (`evaluation_id`) REFERENCES `prompt_human_evaluation` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='人工评估子条目表';

-- 插入示例数据
INSERT INTO `prompt_info` (`name`, `project_id`, `description`, `category`, `created_by`, `current_version`) VALUES
('示例提示词', 'demo_project', '这是一个示例提示词', '问答', 'admin', 1);

INSERT INTO `prompt_version` (`prompt_id`, `version`, `content`, `version_note`, `status`, `created_by`) VALUES
(1, 1, '{"system": "你是一个有用的AI助手", "user": "请回答以下问题：{question}"}', '初始版本', 'published', 'admin');

INSERT INTO `prompt_test_set` (`name`, `project_id`, `created_by`, `csv_data`) VALUES
('基础问答测试集', 'demo_project', 'admin', '[{"input": "什么是人工智能？"}, {"input": "如何学习编程？"}]');

-- 创建视图
CREATE OR REPLACE VIEW `prompt_view` AS
SELECT
    pv.id,
    pi.name,
    pv.version,
    pi.project_id,
    pi.created_by,
    pi.create_time,
    COALESCE(pv.created_by, pi.update_by) as update_by,
    GREATEST(pi.update_time, pv.create_time) as update_time,
    pv.content,
    pi.is_deleted
FROM prompt_info pi
JOIN prompt_version pv ON pi.id = pv.prompt_id
WHERE pi.is_deleted = 0 AND pv.is_deleted = 0;

CREATE OR REPLACE VIEW `prompt_latest_version_view` AS
SELECT
    pi.id as prompt_info_id,
    pv.id,
    pi.name,
    pv.version,
    pi.project_id,
    pi.description,
    pi.category,
    pi.tags,
    pi.created_by,
    pi.create_time,
    pi.update_by,
    pi.update_time,
    pv.content,
    pv.version_note,
    pv.status,
    pi.current_version,
    pi.is_deleted
FROM prompt_info pi
JOIN prompt_version pv ON pi.id = pv.prompt_id AND pi.current_version = pv.version
WHERE pi.is_deleted = 0 AND pv.is_deleted = 0;
