package com.chinaunicom.prompt_manage.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinaunicom.prompt_manage.model.dto.PromptRegistryDTO;
import com.chinaunicom.prompt_manage.model.vo.PromptRegistryVO;
import com.chinaunicom.prompt_manage.service.PromptRegistryService;
import com.chinaunicom.prompt_manage.util.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 提示词注册表控制器
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/prompt-registry")
@RequiredArgsConstructor
public class PromptRegistryController {

    private final PromptRegistryService promptRegistryService;

    /**
     * 分页查询Prompt注册表
     */
    @GetMapping("/page")
    public Result<IPage<PromptRegistryVO>> page(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String projectId,
            @RequestParam(required = false) String name) {

        Page<PromptRegistryVO> page = new Page<>(current, size);
        IPage<PromptRegistryVO> result = promptRegistryService.pageVO(page, projectId, name);
        return Result.success(result);
    }

    /**
     * 根据ID查询Prompt详情
     */
    @GetMapping("/{id}")
    public Result<PromptRegistryVO> getById(@PathVariable Long id) {
        try {
            PromptRegistryVO result = promptRegistryService.getDetailById(id);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询Prompt详情失败，ID: {}", id, e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据项目ID、名称和版本查询
     */
    @GetMapping("/query")
    public Result<PromptRegistryVO> query(
            @RequestParam String projectId,
            @RequestParam String name,
            @RequestParam Long version) {
        try {
            PromptRegistryVO result = promptRegistryService.queryByProjectAndNameAndVersion(projectId, name, version);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询Prompt失败，projectId: {}, name: {}, version: {}", projectId, name, version, e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 创建Prompt
     */
    @PostMapping
    public Result<PromptRegistryVO> create(@Valid @RequestBody PromptRegistryDTO dto) {
        try {
            PromptRegistryVO result = promptRegistryService.create(dto);
            return Result.success("创建成功", result);
        } catch (Exception e) {
            log.error("创建Prompt失败", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }

    /**
     * 更新Prompt
     */
    @PutMapping("/{id}")
    public Result<PromptRegistryVO> update(@PathVariable Long id, @Valid @RequestBody PromptRegistryDTO dto) {
        try {
            PromptRegistryVO result = promptRegistryService.update(id, dto);
            return Result.success("更新成功", result);
        } catch (Exception e) {
            log.error("更新Prompt失败，ID: {}", id, e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除Prompt
     */
    @DeleteMapping("/{id}")
    public Result<Void> delete(@PathVariable Long id) {
        try {
            boolean result = promptRegistryService.delete(id);
            if (result) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除Prompt失败，ID: {}", id, e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }
}
