package com.chinaunicom.prompt_manage.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinaunicom.prompt_manage.model.dto.PromptTestSetDTO;
import com.chinaunicom.prompt_manage.model.vo.PromptTestSetVO;
import com.chinaunicom.prompt_manage.service.PromptTestSetService;
import com.chinaunicom.prompt_manage.util.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 测试集控制器
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/test-set")
@RequiredArgsConstructor
public class PromptTestSetController {

    private final PromptTestSetService promptTestSetService;

    /**
     * 分页查询测试集
     */
    @GetMapping("/page")
    public Result<IPage<PromptTestSetVO>> page(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String projectId,
            @RequestParam(required = false) String name) {

        Page<PromptTestSetVO> page = new Page<>(current, size);
        IPage<PromptTestSetVO> result = promptTestSetService.pageWithoutData(page, projectId, name);
        return Result.success(result);
    }

    /**
     * 根据ID查询测试集详情
     */
    @GetMapping("/{id}")
    public Result<PromptTestSetVO> getById(@PathVariable Long id) {
        try {
            PromptTestSetVO result = promptTestSetService.getDetailById(id);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询测试集详情失败，ID: {}", id, e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 创建测试集
     */
    @PostMapping
    public Result<PromptTestSetVO> create(@Valid @RequestBody PromptTestSetDTO dto) {
        try {
            PromptTestSetVO result = promptTestSetService.create(dto);
            return Result.success("创建成功", result);
        } catch (Exception e) {
            log.error("创建测试集失败", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }

    /**
     * 更新测试集
     */
    @PutMapping("/{id}")
    public Result<PromptTestSetVO> update(@PathVariable Long id, @Valid @RequestBody PromptTestSetDTO dto) {
        try {
            PromptTestSetVO result = promptTestSetService.update(id, dto);
            return Result.success("更新成功", result);
        } catch (Exception e) {
            log.error("更新测试集失败，ID: {}", id, e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除测试集
     */
    @DeleteMapping("/{id}")
    public Result<Void> delete(@PathVariable Long id) {
        try {
            boolean result = promptTestSetService.delete(id);
            if (result) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除测试集失败，ID: {}", id, e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }
}
