package com.chinaunicom.prompt_manage.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinaunicom.prompt_manage.model.dto.PromptExecutionDTO;
import com.chinaunicom.prompt_manage.model.vo.PromptExecutionVO;
import com.chinaunicom.prompt_manage.service.PromptExecutionService;
import com.chinaunicom.prompt_manage.util.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import jakarta.validation.Valid;

/**
 * 提示词执行控制器
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/prompt-execution")
@RequiredArgsConstructor
public class PromptExecutionController {

    private final PromptExecutionService promptExecutionService;

    /**
     * 分页查询执行日志
     */
    @GetMapping("/logs/page")
    public Result<IPage<PromptExecutionVO>> pageExecutionLogs(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String projectId,
            @RequestParam(required = false) String promptName,
            @RequestParam(required = false) String status) {

        Page<PromptExecutionVO> page = new Page<>(current, size);
        IPage<PromptExecutionVO> result = promptExecutionService.pageExecutionLogs(page, projectId, promptName, status);
        return Result.success(result);
    }

    /**
     * 根据ID查询执行日志详情
     */
    @GetMapping("/logs/{id}")
    public Result<PromptExecutionVO> getExecutionLogById(@PathVariable Long id) {
        try {
            PromptExecutionVO result = promptExecutionService.getExecutionLogById(id);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询执行日志详情失败，ID: {}", id, e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 执行Prompt
     */
    @PostMapping("/execute")
    public Result<PromptExecutionVO> execute(@Valid @RequestBody PromptExecutionDTO dto) {
        try {
            PromptExecutionVO result = promptExecutionService.execute(dto);
            return Result.success("执行成功", result);
        } catch (Exception e) {
            log.error("执行Prompt失败", e);
            return Result.error("执行失败：" + e.getMessage());
        }
    }

    /**
     * 流式执行Prompt
     */
    @PostMapping(value = "/execute-streaming", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter executeStreaming(@Valid @RequestBody PromptExecutionDTO dto) {
        SseEmitter emitter = new SseEmitter(600000L); // 10分钟超时
        
        try {
            promptExecutionService.executeStreaming(dto, emitter);
        } catch (Exception e) {
            log.error("流式执行Prompt失败", e);
            emitter.completeWithError(e);
        }
        
        return emitter;
    }

    /**
     * 批量执行Prompt
     */
    @PostMapping("/batch-execute")
    public Result<Void> batchExecute(
            @RequestParam Long promptId,
            @RequestParam Long testSetId,
            @RequestParam String executedBy) {
        try {
            promptExecutionService.batchExecute(promptId, testSetId, executedBy);
            return Result.success("批量执行已启动");
        } catch (Exception e) {
            log.error("批量执行Prompt失败，promptId: {}, testSetId: {}", promptId, testSetId, e);
            return Result.error("批量执行失败：" + e.getMessage());
        }
    }
}
