package com.chinaunicom.prompt_manage.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinaunicom.prompt_manage.model.dto.HumanEvaluationDTO;
import com.chinaunicom.prompt_manage.model.vo.HumanEvaluationVO;
import com.chinaunicom.prompt_manage.service.HumanEvaluationService;
import com.chinaunicom.prompt_manage.util.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;

/**
 * 人工评估控制器
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/human-evaluation")
@RequiredArgsConstructor
public class HumanEvaluationController {

    private final HumanEvaluationService humanEvaluationService;

    /**
     * 分页查询人工评估列表
     */
    @GetMapping("/page")
    public Result<IPage<HumanEvaluationVO>> page(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String projectId,
            @RequestParam(required = false) Long promptId,
            @RequestParam(required = false) String promptName,
            @RequestParam(required = false) Long testSetId,
            @RequestParam(required = false) String testSetName,
            @RequestParam(required = false) String createdBy,
            @RequestParam(required = false) BigDecimal minAvgScore,
            @RequestParam(required = false) BigDecimal maxAvgScore) {

        Page<HumanEvaluationVO> page = new Page<>(current, size);
        IPage<HumanEvaluationVO> result = humanEvaluationService.pageEvaluations(page, projectId, promptId, 
                promptName, testSetId, testSetName, createdBy, minAvgScore, maxAvgScore);
        return Result.success(result);
    }

    /**
     * 根据ID查询评估详情
     */
    @GetMapping("/{id}")
    public Result<HumanEvaluationVO> getById(@PathVariable Long id) {
        try {
            HumanEvaluationVO result = humanEvaluationService.getDetailById(id);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询评估详情失败，ID: {}", id, e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 创建人工评估
     */
    @PostMapping
    public Result<HumanEvaluationVO> create(@Valid @RequestBody HumanEvaluationDTO dto) {
        try {
            HumanEvaluationVO result = humanEvaluationService.create(dto);
            return Result.success("创建成功", result);
        } catch (Exception e) {
            log.error("创建人工评估失败", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }

    /**
     * 更新人工评估
     */
    @PutMapping("/{id}")
    public Result<HumanEvaluationVO> update(@PathVariable Long id, @Valid @RequestBody HumanEvaluationDTO dto) {
        try {
            HumanEvaluationVO result = humanEvaluationService.update(id, dto);
            return Result.success("更新成功", result);
        } catch (Exception e) {
            log.error("更新人工评估失败，ID: {}", id, e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除评估
     */
    @DeleteMapping("/{id}")
    public Result<Void> delete(@PathVariable Long id) {
        try {
            boolean result = humanEvaluationService.delete(id);
            if (result) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除评估失败，ID: {}", id, e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }
}
