package com.chinaunicom.prompt_manage.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 人工评估主表
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("prompt_human_evaluation")
public class HumanEvaluation {

    /**
     * 主键，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联prompt_info.id
     */
    @TableField("prompt_id")
    private Long promptId;

    /**
     * 快照版本（冗余存储）
     */
    @TableField("prompt_version")
    private String promptVersion;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 关联prompt_test_set.id
     */
    @TableField("test_set_id")
    private Long testSetId;

    /**
     * 评估人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 平均得分
     */
    @TableField("avg_score")
    private BigDecimal avgScore;
}
