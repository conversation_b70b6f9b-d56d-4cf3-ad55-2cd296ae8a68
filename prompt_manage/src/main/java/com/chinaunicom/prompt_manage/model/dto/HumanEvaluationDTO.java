package com.chinaunicom.prompt_manage.model.dto;

import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 人工评估数据传输对象
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class HumanEvaluationDTO {

    /**
     * 提示词ID
     */
    @NotNull(message = "提示词ID不能为空")
    private Long promptId;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private String projectId;

    /**
     * 测试集ID
     */
    @NotNull(message = "测试集ID不能为空")
    private Long testSetId;

    /**
     * 评估人ID
     */
    @NotNull(message = "评估人ID不能为空")
    private String createdBy;

    /**
     * 评估详情列表
     */
    @NotEmpty(message = "评估详情不能为空")
    @Valid
    private List<HumanEvaluationDetailDTO> details;

    /**
     * 人工评估详情数据传输对象
     */
    @Data
    public static class HumanEvaluationDetailDTO {

        /**
         * 详情ID（更新时使用）
         */
        private Long id;

        /**
         * 测试输入
         */
        @NotNull(message = "测试输入不能为空")
        private String inputData;

        /**
         * 模型输出
         */
        @NotNull(message = "模型输出不能为空")
        private String outputData;

        /**
         * 期望答案
         */
        private String expectOutput;

        /**
         * 备注
         */
        private String note;

        /**
         * 评分（1-5分）
         */
        @NotNull(message = "评分不能为空")
        private BigDecimal score;
    }
}
