package com.chinaunicom.prompt_manage.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 提示词基本信息表
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("prompt_info")
public class PromptInfo {

    /**
     * 主键，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 提示词名称
     */
    @TableField("name")
    private String name;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 提示词描述
     */
    @TableField("description")
    private String description;

    /**
     * 分类
     */
    @TableField("category")
    private String category;

    /**
     * 标签，逗号分隔
     */
    @TableField("tags")
    private String tags;

    /**
     * 创建人，关联user.id
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人，关联user.id
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 最近修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 当前版本号
     */
    @TableField("current_version")
    private Long currentVersion;

    /**
     * 是否删除（0-未删除，1-已删除）
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
}
