package com.chinaunicom.prompt_manage.model.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 测试集数据传输对象
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class PromptTestSetDTO {

    /**
     * 测试集名称
     */
    @NotBlank(message = "测试集名称不能为空")
    @Size(max = 64, message = "测试集名称长度不能超过64个字符")
    private String name;

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不能为空")
    @Size(max = 64, message = "项目ID长度不能超过64个字符")
    private String projectId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 测试集数据（JSON格式）
     */
    @NotBlank(message = "测试集数据不能为空")
    private String csvData;
}
