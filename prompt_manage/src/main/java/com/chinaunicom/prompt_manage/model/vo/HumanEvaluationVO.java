package com.chinaunicom.prompt_manage.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 人工评估视图对象
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class HumanEvaluationVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 提示词ID
     */
    private Long promptId;

    /**
     * 提示词名称（关联查询）
     */
    private String promptName;

    /**
     * 提示词版本
     */
    private String promptVersion;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 测试集ID
     */
    private Long testSetId;

    /**
     * 测试集名称（关联查询）
     */
    private String testSetName;

    /**
     * 评估人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 平均得分
     */
    private BigDecimal avgScore;

    /**
     * 详情数量
     */
    private Integer detailCount;

    /**
     * 评估详情列表（详情查询时包含）
     */
    private List<HumanEvaluationDetailVO> details;

    /**
     * 人工评估详情视图对象
     */
    @Data
    public static class HumanEvaluationDetailVO {

        /**
         * 详情ID
         */
        private Long id;

        /**
         * 测试输入
         */
        private String inputData;

        /**
         * 模型输出
         */
        private String outputData;

        /**
         * 期望答案
         */
        private String expectOutput;

        /**
         * 备注
         */
        private String note;

        /**
         * 评分
         */
        private BigDecimal score;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;

        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
    }
}
