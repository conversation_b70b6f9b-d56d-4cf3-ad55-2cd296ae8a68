package com.chinaunicom.prompt_manage.model.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 提示词注册表数据传输对象
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class PromptRegistryDTO {

    /**
     * 提示词名称
     */
    @NotBlank(message = "提示词名称不能为空")
    @Size(max = 64, message = "提示词名称长度不能超过64个字符")
    private String name;

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不能为空")
    @Size(max = 64, message = "项目ID长度不能超过64个字符")
    private String projectId;

    /**
     * 提示词描述
     */
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;

    /**
     * 分类
     */
    @Size(max = 32, message = "分类长度不能超过32个字符")
    private String category;

    /**
     * 标签，逗号分隔
     */
    @Size(max = 200, message = "标签长度不能超过200个字符")
    private String tags;

    /**
     * 创建人/更新人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * prompt内容（JSON格式）
     */
    @NotBlank(message = "prompt内容不能为空")
    private String content;

    /**
     * 版本说明
     */
    @Size(max = 500, message = "版本说明长度不能超过500个字符")
    private String versionNote;

    /**
     * 版本状态：draft/published/archived
     */
    private String status = "draft";
}
