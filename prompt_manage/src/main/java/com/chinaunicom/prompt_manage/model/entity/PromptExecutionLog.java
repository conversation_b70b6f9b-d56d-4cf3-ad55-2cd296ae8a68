package com.chinaunicom.prompt_manage.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 提示词执行日志表
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("prompt_execution_log")
public class PromptExecutionLog {

    /**
     * 主键，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联prompt_info.id或prompt_registry.id
     */
    @TableField("prompt_id")
    private Long promptId;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 输入内容
     */
    @TableField("input_data")
    private String inputData;

    /**
     * 模型输出
     */
    @TableField("output_data")
    private String outputData;

    /**
     * 执行状态：success/fail/running
     */
    @TableField("status")
    private String status;

    /**
     * 执行时间
     */
    @TableField("executed_time")
    private LocalDateTime executedTime;

    /**
     * 执行人，关联user.id
     */
    @TableField("executed_by")
    private String executedBy;
}
