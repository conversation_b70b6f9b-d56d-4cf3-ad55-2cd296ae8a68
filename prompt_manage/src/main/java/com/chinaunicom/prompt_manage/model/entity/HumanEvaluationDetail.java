package com.chinaunicom.prompt_manage.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 人工评估子条目表
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("prompt_human_evaluation_detail")
public class HumanEvaluationDetail {

    /**
     * 主键，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联prompt_human_evaluation.id
     */
    @TableField("evaluation_id")
    private Long evaluationId;

    /**
     * 测试输入
     */
    @TableField("input_data")
    private String inputData;

    /**
     * 模型输出
     */
    @TableField("output_data")
    private String outputData;

    /**
     * 期望答案
     */
    @TableField("expect_output")
    private String expectOutput;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 评分
     */
    @TableField("score")
    private BigDecimal score;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
