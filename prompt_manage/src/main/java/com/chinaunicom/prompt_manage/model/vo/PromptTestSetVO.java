package com.chinaunicom.prompt_manage.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 测试集视图对象
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class PromptTestSetVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 测试集名称
     */
    private String name;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 测试集数据（JSON格式）
     * 分页查询时为null，详情查询时包含数据
     */
    private String csvData;

    /**
     * 是否删除
     */
    private Integer isDeleted;
}
