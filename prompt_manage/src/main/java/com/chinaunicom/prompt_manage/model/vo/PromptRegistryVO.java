package com.chinaunicom.prompt_manage.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 提示词注册表视图对象
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class PromptRegistryVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 提示词名称
     */
    private String name;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 提示词描述
     */
    private String description;

    /**
     * 分类
     */
    private String category;

    /**
     * 标签，逗号分隔
     */
    private String tags;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * prompt内容（JSON格式）
     */
    private String content;

    /**
     * 版本说明
     */
    private String versionNote;

    /**
     * 版本状态
     */
    private String status;

    /**
     * 当前版本号
     */
    private Long currentVersion;

    /**
     * 是否删除
     */
    private Integer isDeleted;
}
