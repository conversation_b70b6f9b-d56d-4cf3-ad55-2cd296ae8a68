package com.chinaunicom.prompt_manage.model.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 提示词执行数据传输对象
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class PromptExecutionDTO {

    /**
     * Prompt ID
     */
    private Long promptId;

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * Prompt内容（JSON格式）
     */
    private String promptContent;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 执行人ID
     */
    private String executedBy;

    /**
     * 输入数据（JSON格式）
     */
    private String inputData;
}
