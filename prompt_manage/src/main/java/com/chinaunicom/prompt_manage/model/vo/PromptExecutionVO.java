package com.chinaunicom.prompt_manage.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 提示词执行视图对象
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class PromptExecutionVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * Prompt ID
     */
    private Long promptId;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 输入数据
     */
    private String inputData;

    /**
     * 模型输出
     */
    private String outputData;

    /**
     * 执行状态：success/fail/running
     */
    private String status;

    /**
     * 执行时间
     */
    private LocalDateTime executedTime;

    /**
     * 执行人
     */
    private String executedBy;

    /**
     * Prompt名称（关联查询）
     */
    private String promptName;

    /**
     * 模型名称
     */
    private String modelName;
}
