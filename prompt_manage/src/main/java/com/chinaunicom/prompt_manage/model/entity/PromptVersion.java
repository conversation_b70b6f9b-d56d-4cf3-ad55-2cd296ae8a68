package com.chinaunicom.prompt_manage.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 提示词版本信息表
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("prompt_version")
public class PromptVersion {

    /**
     * 主键，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联prompt_info.id
     */
    @TableField("prompt_id")
    private Long promptId;

    /**
     * 版本号
     */
    @TableField("version")
    private Long version;

    /**
     * prompt内容，JSON格式
     */
    @TableField("content")
    private String content;

    /**
     * 版本说明
     */
    @TableField("version_note")
    private String versionNote;

    /**
     * 状态：draft/published/archived
     */
    @TableField("status")
    private String status;

    /**
     * 版本创建人，关联user.id
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 版本创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 是否删除（0-未删除，1-已删除）
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
}
