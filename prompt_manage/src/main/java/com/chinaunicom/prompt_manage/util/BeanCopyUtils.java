package com.chinaunicom.prompt_manage.util;

import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

/**
 * Bean拷贝工具类
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public class BeanCopyUtils {

    /**
     * 单个对象拷贝
     *
     * @param source 源对象
     * @param target 目标对象类型
     * @param <T>    目标对象类型
     * @return 目标对象实例
     */
    public static <T> T copy(Object source, Class<T> target) {
        if (source == null) {
            return null;
        }
        try {
            T targetInstance = target.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(source, targetInstance);
            return targetInstance;
        } catch (Exception e) {
            throw new RuntimeException("Bean拷贝失败", e);
        }
    }

    /**
     * 单个对象拷贝（使用Supplier）
     *
     * @param source   源对象
     * @param supplier 目标对象供应商
     * @param <T>      目标对象类型
     * @return 目标对象实例
     */
    public static <T> T copy(Object source, Supplier<T> supplier) {
        if (source == null) {
            return null;
        }
        T target = supplier.get();
        BeanUtils.copyProperties(source, target);
        return target;
    }

    /**
     * 集合对象拷贝
     *
     * @param sources 源对象集合
     * @param target  目标对象类型
     * @param <T>     目标对象类型
     * @param <S>     源对象类型
     * @return 目标对象集合
     */
    public static <T, S> List<T> copyList(List<S> sources, Class<T> target) {
        if (sources == null || sources.isEmpty()) {
            return new ArrayList<>();
        }
        List<T> result = new ArrayList<>(sources.size());
        for (S source : sources) {
            result.add(copy(source, target));
        }
        return result;
    }

    /**
     * 集合对象拷贝（使用Supplier）
     *
     * @param sources  源对象集合
     * @param supplier 目标对象供应商
     * @param <T>      目标对象类型
     * @param <S>      源对象类型
     * @return 目标对象集合
     */
    public static <T, S> List<T> copyList(List<S> sources, Supplier<T> supplier) {
        if (sources == null || sources.isEmpty()) {
            return new ArrayList<>();
        }
        List<T> result = new ArrayList<>(sources.size());
        for (S source : sources) {
            result.add(copy(source, supplier));
        }
        return result;
    }
}
