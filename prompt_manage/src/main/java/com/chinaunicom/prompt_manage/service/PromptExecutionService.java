package com.chinaunicom.prompt_manage.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chinaunicom.prompt_manage.model.dto.PromptExecutionDTO;
import com.chinaunicom.prompt_manage.model.entity.PromptExecutionLog;
import com.chinaunicom.prompt_manage.model.vo.PromptExecutionVO;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 提示词执行服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public interface PromptExecutionService extends IService<PromptExecutionLog> {

    /**
     * 分页查询执行日志
     *
     * @param page       分页参数
     * @param projectId  项目ID
     * @param promptName Prompt名称
     * @param status     执行状态
     * @return 分页结果
     */
    IPage<PromptExecutionVO> pageExecutionLogs(Page<PromptExecutionVO> page, String projectId, String promptName, String status);

    /**
     * 根据ID查询执行日志详情
     *
     * @param id 执行日志ID
     * @return 执行日志详情
     */
    PromptExecutionVO getExecutionLogById(Long id);

    /**
     * 执行Prompt
     *
     * @param dto 执行参数
     * @return 执行结果
     */
    PromptExecutionVO execute(PromptExecutionDTO dto);

    /**
     * 流式执行Prompt
     *
     * @param dto     执行参数
     * @param emitter SSE发射器
     */
    void executeStreaming(PromptExecutionDTO dto, SseEmitter emitter);

    /**
     * 批量执行Prompt
     *
     * @param promptId   Prompt ID
     * @param testSetId  测试集ID
     * @param executedBy 执行人ID
     */
    void batchExecute(Long promptId, Long testSetId, String executedBy);
}
