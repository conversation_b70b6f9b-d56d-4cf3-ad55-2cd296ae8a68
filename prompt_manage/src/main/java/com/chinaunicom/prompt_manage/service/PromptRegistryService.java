package com.chinaunicom.prompt_manage.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chinaunicom.prompt_manage.model.dto.PromptRegistryDTO;
import com.chinaunicom.prompt_manage.model.entity.PromptInfo;
import com.chinaunicom.prompt_manage.model.vo.PromptRegistryVO;

/**
 * 提示词注册表服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public interface PromptRegistryService extends IService<PromptInfo> {

    /**
     * 分页查询提示词注册表
     *
     * @param page      分页参数
     * @param projectId 项目ID
     * @param name      提示词名称（模糊查询）
     * @return 分页结果
     */
    IPage<PromptRegistryVO> pageVO(Page<PromptRegistryVO> page, String projectId, String name);

    /**
     * 根据ID查询提示词详情
     *
     * @param id 提示词ID
     * @return 提示词详情
     */
    PromptRegistryVO getDetailById(Long id);

    /**
     * 根据项目ID、名称和版本查询提示词
     *
     * @param projectId 项目ID
     * @param name      提示词名称
     * @param version   版本号
     * @return 提示词详情
     */
    PromptRegistryVO queryByProjectAndNameAndVersion(String projectId, String name, Long version);

    /**
     * 创建提示词
     *
     * @param dto 创建参数
     * @return 创建结果
     */
    PromptRegistryVO create(PromptRegistryDTO dto);

    /**
     * 更新提示词
     *
     * @param id  提示词ID
     * @param dto 更新参数
     * @return 更新结果
     */
    PromptRegistryVO update(Long id, PromptRegistryDTO dto);

    /**
     * 删除提示词
     *
     * @param id 提示词ID
     * @return 删除结果
     */
    boolean delete(Long id);
}
