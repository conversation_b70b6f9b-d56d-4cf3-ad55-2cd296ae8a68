package com.chinaunicom.prompt_manage.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chinaunicom.prompt_manage.model.dto.HumanEvaluationDTO;
import com.chinaunicom.prompt_manage.model.entity.HumanEvaluation;
import com.chinaunicom.prompt_manage.model.vo.HumanEvaluationVO;

import java.math.BigDecimal;

/**
 * 人工评估服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public interface HumanEvaluationService extends IService<HumanEvaluation> {

    /**
     * 分页查询人工评估列表
     *
     * @param page        分页参数
     * @param projectId   项目ID
     * @param promptId    提示词ID
     * @param promptName  提示词名称（模糊查询）
     * @param testSetId   测试集ID
     * @param testSetName 测试集名称（模糊查询）
     * @param createdBy   评估人
     * @param minAvgScore 最小平均分
     * @param maxAvgScore 最大平均分
     * @return 分页结果
     */
    IPage<HumanEvaluationVO> pageEvaluations(Page<HumanEvaluationVO> page, 
                                             String projectId, 
                                             Long promptId, 
                                             String promptName, 
                                             Long testSetId, 
                                             String testSetName, 
                                             String createdBy, 
                                             BigDecimal minAvgScore, 
                                             BigDecimal maxAvgScore);

    /**
     * 根据ID查询评估详情（包含详情列表）
     *
     * @param id 评估ID
     * @return 评估详情
     */
    HumanEvaluationVO getDetailById(Long id);

    /**
     * 创建人工评估
     *
     * @param dto 创建参数
     * @return 创建结果
     */
    HumanEvaluationVO create(HumanEvaluationDTO dto);

    /**
     * 更新人工评估
     *
     * @param id  评估ID
     * @param dto 更新参数
     * @return 更新结果
     */
    HumanEvaluationVO update(Long id, HumanEvaluationDTO dto);

    /**
     * 删除评估
     *
     * @param id 评估ID
     * @return 删除结果
     */
    boolean delete(Long id);
}
