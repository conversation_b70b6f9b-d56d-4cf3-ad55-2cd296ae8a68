package com.chinaunicom.prompt_manage.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chinaunicom.prompt_manage.exception.BusinessException;
import com.chinaunicom.prompt_manage.model.dto.PromptTestSetDTO;
import com.chinaunicom.prompt_manage.model.entity.PromptTestSet;
import com.chinaunicom.prompt_manage.model.vo.PromptTestSetVO;
import com.chinaunicom.prompt_manage.repository.PromptTestSetMapper;
import com.chinaunicom.prompt_manage.service.PromptTestSetService;
import com.chinaunicom.prompt_manage.util.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 测试集服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PromptTestSetServiceImpl extends ServiceImpl<PromptTestSetMapper, PromptTestSet> 
        implements PromptTestSetService {

    private final PromptTestSetMapper promptTestSetMapper;

    @Override
    public IPage<PromptTestSetVO> pageWithoutData(Page<PromptTestSetVO> page, String projectId, String name) {
        return promptTestSetMapper.selectTestSetPageWithoutData(page, projectId, name);
    }

    @Override
    public PromptTestSetVO getDetailById(Long id) {
        if (id == null) {
            throw new BusinessException("ID不能为空");
        }
        
        PromptTestSetVO result = promptTestSetMapper.selectTestSetById(id);
        if (result == null) {
            throw new BusinessException("测试集不存在");
        }
        
        return result;
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public PromptTestSetVO create(PromptTestSetDTO dto) {
        // 参数校验
        validateCreateParams(dto);
        
        // 创建测试集
        PromptTestSet testSet = BeanCopyUtils.copy(dto, PromptTestSet.class);
        save(testSet);
        
        log.info("创建测试集成功，ID: {}", testSet.getId());
        
        return getDetailById(testSet.getId());
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public PromptTestSetVO update(Long id, PromptTestSetDTO dto) {
        // 检查测试集是否存在
        PromptTestSet existingTestSet = getById(id);
        if (existingTestSet == null) {
            throw new BusinessException("测试集不存在");
        }
        
        // 更新测试集
        PromptTestSet testSet = BeanCopyUtils.copy(dto, PromptTestSet.class);
        testSet.setId(id);
        updateById(testSet);
        
        log.info("更新测试集成功，ID: {}", id);
        
        return getDetailById(id);
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public boolean delete(Long id) {
        if (id == null) {
            throw new BusinessException("ID不能为空");
        }
        
        boolean result = removeById(id);
        if (result) {
            log.info("删除测试集成功，ID: {}", id);
        }
        
        return result;
    }

    /**
     * 校验创建参数
     */
    private void validateCreateParams(PromptTestSetDTO dto) {
        if (StringUtils.isBlank(dto.getName())) {
            throw new BusinessException("测试集名称不能为空");
        }
        if (StringUtils.isBlank(dto.getProjectId())) {
            throw new BusinessException("项目ID不能为空");
        }
        if (StringUtils.isBlank(dto.getCsvData())) {
            throw new BusinessException("测试集数据不能为空");
        }
    }
}
