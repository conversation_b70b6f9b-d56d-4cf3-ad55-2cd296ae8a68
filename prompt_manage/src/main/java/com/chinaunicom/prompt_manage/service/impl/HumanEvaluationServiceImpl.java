package com.chinaunicom.prompt_manage.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chinaunicom.prompt_manage.exception.BusinessException;
import com.chinaunicom.prompt_manage.model.dto.HumanEvaluationDTO;
import com.chinaunicom.prompt_manage.model.entity.HumanEvaluation;
import com.chinaunicom.prompt_manage.model.entity.HumanEvaluationDetail;
import com.chinaunicom.prompt_manage.model.vo.HumanEvaluationVO;
import com.chinaunicom.prompt_manage.repository.HumanEvaluationDetailMapper;
import com.chinaunicom.prompt_manage.repository.HumanEvaluationMapper;
import com.chinaunicom.prompt_manage.service.HumanEvaluationService;
import com.chinaunicom.prompt_manage.util.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 人工评估服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HumanEvaluationServiceImpl extends ServiceImpl<HumanEvaluationMapper, HumanEvaluation> 
        implements HumanEvaluationService {

    private final HumanEvaluationMapper humanEvaluationMapper;
    private final HumanEvaluationDetailMapper humanEvaluationDetailMapper;

    @Override
    public IPage<HumanEvaluationVO> pageEvaluations(Page<HumanEvaluationVO> page, 
                                                    String projectId, 
                                                    Long promptId, 
                                                    String promptName, 
                                                    Long testSetId, 
                                                    String testSetName, 
                                                    String createdBy, 
                                                    BigDecimal minAvgScore, 
                                                    BigDecimal maxAvgScore) {
        return humanEvaluationMapper.selectHumanEvaluationPage(page, projectId, promptId, promptName, 
                testSetId, testSetName, createdBy, minAvgScore, maxAvgScore);
    }

    @Override
    public HumanEvaluationVO getDetailById(Long id) {
        if (id == null) {
            throw new BusinessException("ID不能为空");
        }
        
        // 查询评估主表信息
        HumanEvaluationVO result = humanEvaluationMapper.selectHumanEvaluationById(id);
        if (result == null) {
            throw new BusinessException("人工评估不存在");
        }
        
        // 查询评估详情列表
        List<HumanEvaluationVO.HumanEvaluationDetailVO> details = 
                humanEvaluationDetailMapper.selectDetailsByEvaluationId(id);
        result.setDetails(details);
        result.setDetailCount(details.size());
        
        return result;
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public HumanEvaluationVO create(HumanEvaluationDTO dto) {
        // 参数校验
        validateCreateParams(dto);
        
        // 计算平均分
        BigDecimal avgScore = calculateAvgScore(dto.getDetails());
        
        // 创建评估主表
        HumanEvaluation evaluation = BeanCopyUtils.copy(dto, HumanEvaluation.class);
        evaluation.setAvgScore(avgScore);
        // TODO: 获取当前Prompt版本
        evaluation.setPromptVersion("1");
        save(evaluation);
        
        // 创建评估详情
        for (HumanEvaluationDTO.HumanEvaluationDetailDTO detailDTO : dto.getDetails()) {
            HumanEvaluationDetail detail = BeanCopyUtils.copy(detailDTO, HumanEvaluationDetail.class);
            detail.setEvaluationId(evaluation.getId());
            humanEvaluationDetailMapper.insert(detail);
        }
        
        log.info("创建人工评估成功，ID: {}, 平均分: {}", evaluation.getId(), avgScore);
        
        return getDetailById(evaluation.getId());
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public HumanEvaluationVO update(Long id, HumanEvaluationDTO dto) {
        // 检查评估是否存在
        HumanEvaluation existingEvaluation = getById(id);
        if (existingEvaluation == null) {
            throw new BusinessException("人工评估不存在");
        }
        
        // 计算平均分
        BigDecimal avgScore = calculateAvgScore(dto.getDetails());
        
        // 更新评估主表
        HumanEvaluation evaluation = BeanCopyUtils.copy(dto, HumanEvaluation.class);
        evaluation.setId(id);
        evaluation.setAvgScore(avgScore);
        updateById(evaluation);
        
        // 删除原有详情
        LambdaQueryWrapper<HumanEvaluationDetail> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(HumanEvaluationDetail::getEvaluationId, id);
        humanEvaluationDetailMapper.delete(deleteWrapper);
        
        // 创建新的评估详情
        for (HumanEvaluationDTO.HumanEvaluationDetailDTO detailDTO : dto.getDetails()) {
            HumanEvaluationDetail detail = BeanCopyUtils.copy(detailDTO, HumanEvaluationDetail.class);
            detail.setEvaluationId(id);
            humanEvaluationDetailMapper.insert(detail);
        }
        
        log.info("更新人工评估成功，ID: {}, 新平均分: {}", id, avgScore);
        
        return getDetailById(id);
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public boolean delete(Long id) {
        if (id == null) {
            throw new BusinessException("ID不能为空");
        }
        
        // 删除评估详情
        LambdaQueryWrapper<HumanEvaluationDetail> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(HumanEvaluationDetail::getEvaluationId, id);
        humanEvaluationDetailMapper.delete(deleteWrapper);
        
        // 删除评估主表
        boolean result = removeById(id);
        if (result) {
            log.info("删除人工评估成功，ID: {}", id);
        }
        
        return result;
    }

    /**
     * 校验创建参数
     */
    private void validateCreateParams(HumanEvaluationDTO dto) {
        if (dto.getPromptId() == null) {
            throw new BusinessException("提示词ID不能为空");
        }
        if (dto.getTestSetId() == null) {
            throw new BusinessException("测试集ID不能为空");
        }
        if (dto.getDetails() == null || dto.getDetails().isEmpty()) {
            throw new BusinessException("评估详情不能为空");
        }
        
        // 校验评分范围
        for (HumanEvaluationDTO.HumanEvaluationDetailDTO detail : dto.getDetails()) {
            if (detail.getScore() == null || 
                detail.getScore().compareTo(BigDecimal.ONE) < 0 || 
                detail.getScore().compareTo(BigDecimal.valueOf(5)) > 0) {
                throw new BusinessException("评分必须在1-5分之间");
            }
        }
    }

    /**
     * 计算平均分
     */
    private BigDecimal calculateAvgScore(List<HumanEvaluationDTO.HumanEvaluationDetailDTO> details) {
        if (details == null || details.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal totalScore = details.stream()
                .map(HumanEvaluationDTO.HumanEvaluationDetailDTO::getScore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        return totalScore.divide(BigDecimal.valueOf(details.size()), 2, RoundingMode.HALF_UP);
    }
}
