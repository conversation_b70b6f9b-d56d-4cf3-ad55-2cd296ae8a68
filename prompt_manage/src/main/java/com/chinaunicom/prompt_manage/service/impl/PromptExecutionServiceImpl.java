package com.chinaunicom.prompt_manage.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chinaunicom.prompt_manage.exception.BusinessException;
import com.chinaunicom.prompt_manage.model.dto.PromptExecutionDTO;
import com.chinaunicom.prompt_manage.model.entity.PromptExecutionLog;
import com.chinaunicom.prompt_manage.model.vo.PromptExecutionVO;
import com.chinaunicom.prompt_manage.repository.PromptExecutionLogMapper;
import com.chinaunicom.prompt_manage.service.PromptExecutionService;
import com.chinaunicom.prompt_manage.util.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.LocalDateTime;

/**
 * 提示词执行服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PromptExecutionServiceImpl extends ServiceImpl<PromptExecutionLogMapper, PromptExecutionLog> 
        implements PromptExecutionService {

    private final PromptExecutionLogMapper promptExecutionLogMapper;

    @Override
    public IPage<PromptExecutionVO> pageExecutionLogs(Page<PromptExecutionVO> page, String projectId, String promptName, String status) {
        return promptExecutionLogMapper.selectExecutionLogPage(page, projectId, promptName, status);
    }

    @Override
    public PromptExecutionVO getExecutionLogById(Long id) {
        if (id == null) {
            throw new BusinessException("ID不能为空");
        }
        
        PromptExecutionVO result = promptExecutionLogMapper.selectExecutionLogById(id);
        if (result == null) {
            throw new BusinessException("执行日志不存在");
        }
        
        return result;
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public PromptExecutionVO execute(PromptExecutionDTO dto) {
        // 参数校验
        validateExecutionParams(dto);
        
        // 创建执行日志
        PromptExecutionLog executionLog = createExecutionLog(dto);
        
        try {
            // TODO: 调用AI模型执行Prompt
            String outputData = executePromptWithAI(dto);
            
            // 更新执行结果
            executionLog.setOutputData(outputData);
            executionLog.setStatus("SUCCESS");
            updateById(executionLog);
            
            log.info("Prompt执行成功，ID: {}", executionLog.getId());
            
        } catch (Exception e) {
            log.error("Prompt执行失败，ID: {}", executionLog.getId(), e);
            
            // 更新失败状态
            executionLog.setOutputData("执行失败：" + e.getMessage());
            executionLog.setStatus("FAIL");
            updateById(executionLog);
            
            throw new BusinessException("Prompt执行失败：" + e.getMessage());
        }
        
        return BeanCopyUtils.copy(executionLog, PromptExecutionVO.class);
    }

    @Override
    @Async
    public void executeStreaming(PromptExecutionDTO dto, SseEmitter emitter) {
        // 创建执行日志
        PromptExecutionLog executionLog = createExecutionLog(dto);
        executionLog.setStatus("RUNNING");
        updateById(executionLog);
        
        try {
            // 发送开始事件
            sendSseEvent(emitter, "start", executionLog.getId().toString(), "RUNNING", null);
            
            // TODO: 实现流式执行逻辑
            String result = executePromptStreamingWithAI(dto, emitter);
            
            // 更新执行结果
            executionLog.setOutputData(result);
            executionLog.setStatus("SUCCESS");
            updateById(executionLog);
            
            // 发送完成事件
            sendSseEvent(emitter, "complete", result, "SUCCESS", null);
            
            log.info("流式Prompt执行成功，ID: {}", executionLog.getId());
            
        } catch (Exception e) {
            log.error("流式Prompt执行失败，ID: {}", executionLog.getId(), e);
            
            // 更新失败状态
            executionLog.setOutputData("执行失败：" + e.getMessage());
            executionLog.setStatus("FAIL");
            updateById(executionLog);
            
            // 发送错误事件
            sendSseEvent(emitter, "error", null, "FAIL", e.getMessage());
            
        } finally {
            emitter.complete();
        }
    }

    @Override
    @Async
    public void batchExecute(Long promptId, Long testSetId, String executedBy) {
        // TODO: 实现批量执行逻辑
        log.info("开始批量执行，promptId: {}, testSetId: {}, executedBy: {}", promptId, testSetId, executedBy);
        
        try {
            // 1. 获取测试集数据
            // 2. 循环执行每个测试用例
            // 3. 记录执行结果
            
            log.info("批量执行完成，promptId: {}, testSetId: {}", promptId, testSetId);
            
        } catch (Exception e) {
            log.error("批量执行失败，promptId: {}, testSetId: {}", promptId, testSetId, e);
        }
    }

    /**
     * 校验执行参数
     */
    private void validateExecutionParams(PromptExecutionDTO dto) {
        if (StringUtils.isBlank(dto.getProjectId())) {
            throw new BusinessException("项目ID不能为空");
        }
    }

    /**
     * 创建执行日志
     */
    private PromptExecutionLog createExecutionLog(PromptExecutionDTO dto) {
        PromptExecutionLog executionLog = BeanCopyUtils.copy(dto, PromptExecutionLog.class);
        executionLog.setExecutedTime(LocalDateTime.now());
        executionLog.setStatus("RUNNING");
        save(executionLog);
        return executionLog;
    }

    /**
     * 执行Prompt（同步）
     */
    private String executePromptWithAI(PromptExecutionDTO dto) {
        // TODO: 集成LangChain4j调用AI模型
        return "模拟AI响应结果";
    }

    /**
     * 执行Prompt（流式）
     */
    private String executePromptStreamingWithAI(PromptExecutionDTO dto, SseEmitter emitter) {
        // TODO: 集成LangChain4j实现流式调用
        StringBuilder result = new StringBuilder();
        
        // 模拟流式响应
        String[] tokens = {"这是", "一个", "模拟的", "流式", "响应", "结果"};
        for (String token : tokens) {
            result.append(token);
            sendSseEvent(emitter, "token", token, "RUNNING", null);
            
            try {
                Thread.sleep(100); // 模拟延迟
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        return result.toString();
    }

    /**
     * 发送SSE事件
     */
    private void sendSseEvent(SseEmitter emitter, String type, String content, String status, String errorMessage) {
        try {
            SseEmitter.SseEventBuilder event = SseEmitter.event()
                    .data(String.format("{\"type\":\"%s\",\"content\":\"%s\",\"status\":\"%s\",\"timestamp\":%d,\"errorMessage\":\"%s\"}", 
                            type, content != null ? content : "", status, System.currentTimeMillis(), errorMessage != null ? errorMessage : ""));
            emitter.send(event);
        } catch (IOException e) {
            log.error("发送SSE事件失败", e);
            emitter.completeWithError(e);
        }
    }
}
