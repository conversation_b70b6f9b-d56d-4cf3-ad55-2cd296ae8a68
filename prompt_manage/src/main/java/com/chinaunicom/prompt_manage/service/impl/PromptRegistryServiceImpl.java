package com.chinaunicom.prompt_manage.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chinaunicom.prompt_manage.exception.BusinessException;
import com.chinaunicom.prompt_manage.model.dto.PromptRegistryDTO;
import com.chinaunicom.prompt_manage.model.entity.PromptInfo;
import com.chinaunicom.prompt_manage.model.entity.PromptVersion;
import com.chinaunicom.prompt_manage.model.vo.PromptRegistryVO;
import com.chinaunicom.prompt_manage.repository.PromptInfoMapper;
import com.chinaunicom.prompt_manage.repository.PromptVersionMapper;
import com.chinaunicom.prompt_manage.service.PromptRegistryService;
import com.chinaunicom.prompt_manage.util.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 提示词注册表服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PromptRegistryServiceImpl extends ServiceImpl<PromptInfoMapper, PromptInfo> 
        implements PromptRegistryService {

    private final PromptInfoMapper promptInfoMapper;
    private final PromptVersionMapper promptVersionMapper;

    @Override
    public IPage<PromptRegistryVO> pageVO(Page<PromptRegistryVO> page, String projectId, String name) {
        return promptInfoMapper.selectPromptRegistryPage(page, projectId, name);
    }

    @Override
    public PromptRegistryVO getDetailById(Long id) {
        if (id == null) {
            throw new BusinessException("ID不能为空");
        }
        
        PromptRegistryVO result = promptInfoMapper.selectPromptRegistryById(id);
        if (result == null) {
            throw new BusinessException("Prompt不存在");
        }
        
        return result;
    }

    @Override
    public PromptRegistryVO queryByProjectAndNameAndVersion(String projectId, String name, Long version) {
        if (StringUtils.isBlank(projectId) || StringUtils.isBlank(name) || version == null) {
            throw new BusinessException("项目ID、名称和版本号不能为空");
        }
        
        PromptRegistryVO result = promptInfoMapper.selectPromptRegistryByProjectAndNameAndVersion(projectId, name, version);
        if (result == null) {
            throw new BusinessException("Prompt不存在");
        }
        
        return result;
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public PromptRegistryVO create(PromptRegistryDTO dto) {
        // 参数校验
        validateCreateParams(dto);
        
        // 获取下一个版本号
        Long nextVersion = getNextVersion(dto.getProjectId(), dto.getName());
        
        // 创建PromptInfo
        PromptInfo promptInfo = BeanCopyUtils.copy(dto, PromptInfo.class);
        promptInfo.setCurrentVersion(nextVersion);
        save(promptInfo);
        
        // 创建PromptVersion
        PromptVersion promptVersion = new PromptVersion();
        promptVersion.setPromptId(promptInfo.getId());
        promptVersion.setVersion(nextVersion);
        promptVersion.setContent(dto.getContent());
        promptVersion.setVersionNote(dto.getVersionNote());
        promptVersion.setStatus(dto.getStatus());
        promptVersion.setCreatedBy(dto.getCreatedBy());
        promptVersionMapper.insert(promptVersion);
        
        log.info("创建Prompt成功，ID: {}, 版本: {}", promptInfo.getId(), nextVersion);
        
        return getDetailById(promptInfo.getId());
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public PromptRegistryVO update(Long id, PromptRegistryDTO dto) {
        // 检查Prompt是否存在
        PromptInfo existingPrompt = getById(id);
        if (existingPrompt == null) {
            throw new BusinessException("Prompt不存在");
        }
        
        // 获取下一个版本号
        Long nextVersion = existingPrompt.getCurrentVersion() + 1;
        
        // 更新PromptInfo
        PromptInfo promptInfo = BeanCopyUtils.copy(dto, PromptInfo.class);
        promptInfo.setId(id);
        promptInfo.setCurrentVersion(nextVersion);
        updateById(promptInfo);
        
        // 创建新版本
        PromptVersion promptVersion = new PromptVersion();
        promptVersion.setPromptId(id);
        promptVersion.setVersion(nextVersion);
        promptVersion.setContent(dto.getContent());
        promptVersion.setVersionNote(dto.getVersionNote());
        promptVersion.setStatus(dto.getStatus());
        promptVersion.setCreatedBy(dto.getUpdateBy());
        promptVersionMapper.insert(promptVersion);
        
        log.info("更新Prompt成功，ID: {}, 新版本: {}", id, nextVersion);
        
        return getDetailById(id);
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public boolean delete(Long id) {
        if (id == null) {
            throw new BusinessException("ID不能为空");
        }
        
        boolean result = removeById(id);
        if (result) {
            log.info("删除Prompt成功，ID: {}", id);
        }
        
        return result;
    }

    /**
     * 校验创建参数
     */
    private void validateCreateParams(PromptRegistryDTO dto) {
        if (StringUtils.isBlank(dto.getName())) {
            throw new BusinessException("提示词名称不能为空");
        }
        if (StringUtils.isBlank(dto.getProjectId())) {
            throw new BusinessException("项目ID不能为空");
        }
        if (StringUtils.isBlank(dto.getContent())) {
            throw new BusinessException("提示词内容不能为空");
        }
    }

    /**
     * 获取下一个版本号
     */
    private Long getNextVersion(String projectId, String name) {
        Long nextVersion = promptInfoMapper.selectNextVersion(projectId, name);
        return nextVersion != null ? nextVersion : 1L;
    }
}
