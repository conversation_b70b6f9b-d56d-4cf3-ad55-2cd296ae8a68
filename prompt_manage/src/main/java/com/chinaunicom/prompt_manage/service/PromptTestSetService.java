package com.chinaunicom.prompt_manage.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chinaunicom.prompt_manage.model.dto.PromptTestSetDTO;
import com.chinaunicom.prompt_manage.model.entity.PromptTestSet;
import com.chinaunicom.prompt_manage.model.vo.PromptTestSetVO;

/**
 * 测试集服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public interface PromptTestSetService extends IService<PromptTestSet> {

    /**
     * 分页查询测试集（不包含数据）
     *
     * @param page      分页参数
     * @param projectId 项目ID
     * @param name      测试集名称（模糊查询）
     * @return 分页结果
     */
    IPage<PromptTestSetVO> pageWithoutData(Page<PromptTestSetVO> page, String projectId, String name);

    /**
     * 根据ID查询测试集详情（包含数据）
     *
     * @param id 测试集ID
     * @return 测试集详情
     */
    PromptTestSetVO getDetailById(Long id);

    /**
     * 创建测试集
     *
     * @param dto 创建参数
     * @return 创建结果
     */
    PromptTestSetVO create(PromptTestSetDTO dto);

    /**
     * 更新测试集
     *
     * @param id  测试集ID
     * @param dto 更新参数
     * @return 更新结果
     */
    PromptTestSetVO update(Long id, PromptTestSetDTO dto);

    /**
     * 删除测试集
     *
     * @param id 测试集ID
     * @return 删除结果
     */
    boolean delete(Long id);
}
