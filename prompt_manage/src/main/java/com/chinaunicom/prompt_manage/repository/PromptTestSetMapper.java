package com.chinaunicom.prompt_manage.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinaunicom.prompt_manage.model.entity.PromptTestSet;
import com.chinaunicom.prompt_manage.model.vo.PromptTestSetVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 测试集Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Mapper
public interface PromptTestSetMapper extends BaseMapper<PromptTestSet> {

    /**
     * 分页查询测试集（不包含数据）
     *
     * @param page      分页参数
     * @param projectId 项目ID
     * @param name      测试集名称（模糊查询）
     * @return 分页结果
     */
    IPage<PromptTestSetVO> selectTestSetPageWithoutData(Page<PromptTestSetVO> page, 
                                                        @Param("projectId") String projectId, 
                                                        @Param("name") String name);

    /**
     * 根据ID查询测试集详情（包含数据）
     *
     * @param id 测试集ID
     * @return 测试集详情
     */
    PromptTestSetVO selectTestSetById(@Param("id") Long id);
}
