package com.chinaunicom.prompt_manage.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinaunicom.prompt_manage.model.entity.HumanEvaluation;
import com.chinaunicom.prompt_manage.model.vo.HumanEvaluationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * 人工评估Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Mapper
public interface HumanEvaluationMapper extends BaseMapper<HumanEvaluation> {

    /**
     * 分页查询人工评估列表
     *
     * @param page        分页参数
     * @param projectId   项目ID
     * @param promptId    提示词ID
     * @param promptName  提示词名称（模糊查询）
     * @param testSetId   测试集ID
     * @param testSetName 测试集名称（模糊查询）
     * @param createdBy   评估人
     * @param minAvgScore 最小平均分
     * @param maxAvgScore 最大平均分
     * @return 分页结果
     */
    IPage<HumanEvaluationVO> selectHumanEvaluationPage(Page<HumanEvaluationVO> page, 
                                                       @Param("projectId") String projectId, 
                                                       @Param("promptId") Long promptId, 
                                                       @Param("promptName") String promptName, 
                                                       @Param("testSetId") Long testSetId, 
                                                       @Param("testSetName") String testSetName, 
                                                       @Param("createdBy") String createdBy, 
                                                       @Param("minAvgScore") BigDecimal minAvgScore, 
                                                       @Param("maxAvgScore") BigDecimal maxAvgScore);

    /**
     * 根据ID查询人工评估详情（不包含详情列表）
     *
     * @param id 评估ID
     * @return 评估详情
     */
    HumanEvaluationVO selectHumanEvaluationById(@Param("id") Long id);
}
