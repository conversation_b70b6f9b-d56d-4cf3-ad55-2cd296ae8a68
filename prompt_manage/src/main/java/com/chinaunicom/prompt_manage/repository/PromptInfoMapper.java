package com.chinaunicom.prompt_manage.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinaunicom.prompt_manage.model.entity.PromptInfo;
import com.chinaunicom.prompt_manage.model.vo.PromptRegistryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 提示词基本信息Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Mapper
public interface PromptInfoMapper extends BaseMapper<PromptInfo> {

    /**
     * 分页查询提示词注册表视图
     *
     * @param page      分页参数
     * @param projectId 项目ID
     * @param name      提示词名称（模糊查询）
     * @return 分页结果
     */
    IPage<PromptRegistryVO> selectPromptRegistryPage(Page<PromptRegistryVO> page, 
                                                     @Param("projectId") String projectId, 
                                                     @Param("name") String name);

    /**
     * 根据ID查询提示词注册表详情
     *
     * @param id 提示词ID
     * @return 提示词详情
     */
    PromptRegistryVO selectPromptRegistryById(@Param("id") Long id);

    /**
     * 根据项目ID、名称和版本查询提示词
     *
     * @param projectId 项目ID
     * @param name      提示词名称
     * @param version   版本号
     * @return 提示词详情
     */
    PromptRegistryVO selectPromptRegistryByProjectAndNameAndVersion(@Param("projectId") String projectId, 
                                                                    @Param("name") String name, 
                                                                    @Param("version") Long version);

    /**
     * 获取下一个版本号
     *
     * @param projectId 项目ID
     * @param name      提示词名称
     * @return 下一个版本号
     */
    Long selectNextVersion(@Param("projectId") String projectId, @Param("name") String name);
}
