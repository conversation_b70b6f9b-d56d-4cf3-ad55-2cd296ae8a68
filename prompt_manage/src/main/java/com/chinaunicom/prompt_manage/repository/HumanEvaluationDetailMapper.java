package com.chinaunicom.prompt_manage.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chinaunicom.prompt_manage.model.entity.HumanEvaluationDetail;
import com.chinaunicom.prompt_manage.model.vo.HumanEvaluationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 人工评估详情Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Mapper
public interface HumanEvaluationDetailMapper extends BaseMapper<HumanEvaluationDetail> {

    /**
     * 根据评估ID查询评估详情列表
     *
     * @param evaluationId 评估ID
     * @return 评估详情列表
     */
    List<HumanEvaluationVO.HumanEvaluationDetailVO> selectDetailsByEvaluationId(@Param("evaluationId") Long evaluationId);
}
