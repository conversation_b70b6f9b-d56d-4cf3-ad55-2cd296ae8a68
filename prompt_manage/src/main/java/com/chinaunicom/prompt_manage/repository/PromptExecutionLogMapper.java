package com.chinaunicom.prompt_manage.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinaunicom.prompt_manage.model.entity.PromptExecutionLog;
import com.chinaunicom.prompt_manage.model.vo.PromptExecutionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 提示词执行日志Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Mapper
public interface PromptExecutionLogMapper extends BaseMapper<PromptExecutionLog> {

    /**
     * 分页查询执行日志
     *
     * @param page       分页参数
     * @param projectId  项目ID
     * @param promptName Prompt名称
     * @param status     执行状态
     * @return 分页结果
     */
    IPage<PromptExecutionVO> selectExecutionLogPage(Page<PromptExecutionVO> page, 
                                                    @Param("projectId") String projectId, 
                                                    @Param("promptName") String promptName, 
                                                    @Param("status") String status);

    /**
     * 根据ID查询执行日志详情
     *
     * @param id 执行日志ID
     * @return 执行日志详情
     */
    PromptExecutionVO selectExecutionLogById(@Param("id") Long id);
}
