<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.aiAgents.dao.mapper.AiPendingReviewMapper">

    <resultMap id="PendingReviewResultMap" type="com.chinaunicom.aiAgents.dao.domain.AiPendingReview">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="pendId" column="pend_id" jdbcType="VARCHAR"/>
        <result property="pendModel" column="pend_model" jdbcType="VARCHAR"/>
        <result property="pendFun" column="pend_fun" jdbcType="VARCHAR"/>
        <result property="pendAct" column="pend_act" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="aiSummary" column="ai_summary" jdbcType="VARCHAR"/>
        <result property="backgroundInfo" column="background_info" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="countersign" column="countersign" jdbcType="VARCHAR"/>
        <result property="stateShow" column="state_show" jdbcType="VARCHAR"/>
        <result property="stateLogic" column="state_logic" jdbcType="INTEGER"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="belongModule" column="belong_module" jdbcType="VARCHAR"/>
        <result property="agentId" column="agent_id" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,pend_id,pend_model,
        pend_fun,pend_act,title,
        ai_summary,background_info,type,
        countersign,state_show,state_logic,
        url,belong_module,agent_id,
        creator,create_time,update_time,
        del_flag
    </sql>

    <insert id="addPending" parameterType="com.chinaunicom.aiAgents.dao.domain.AiPendingReview">
        INSERT INTO ai_pending_review (
        id, pend_id,pend_model,pend_fun,pend_act,title, ai_summary, background_info, type, countersign, state_show, state_logic,
        url, belong_module, agent_id,creator, create_time, update_time, del_flag
        )
        VALUES (
        #{id}, #{pendId},#{pendModel}, #{pendFun},#{pendAct},#{title}, #{aiSummary}, #{backgroundInfo}, #{type}, #{countersign}, #{stateShow}, #{stateLogic},
        #{url},#{belongModule}, #{agentId},#{creator}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0
        )
    </insert>

    <insert id="addPendingResponsibleUser">
        INSERT INTO ai_pending_review_user (
        todo_id, user_id, state_show, state_logic, is_changed_msg, buttons_json
        )
        VALUES
        <foreach collection="userIds" item="item" separator=",">
            (
            #{todoId}, #{item}, #{stateShow}, #{stateLogic}, 0, #{buttonsJson}
            )
        </foreach>
    </insert>

    <select id="queryPendingById" resultType="com.chinaunicom.aiAgents.dao.domain.AiPendingReview">
        select * FROM ai_pending_review where id = #{Id} limit 1
    </select>

    <select id="queryItemByPendIdAndPendFunAndPendModel" resultMap="PendingReviewResultMap">
        select *
        FROM ai_pending_review
        where pend_id = #{pendId} and pend_fun = #{pendFun} and pend_model = #{pendModel}
    </select>

    <select id="queryUserPending">
        select * FROM ai_pending_review_user where user_id = #{userId} and todo_id = #{pendingId} limit 1
    </select>

    <update id="updateUserPendingState">
        update ai_pending_review_user
        set state_show = #{stateShow}, state_logic = #{stateLogic}
        where todo_id= #{pendingId} and user_id=#{userId}
    </update>

    <update id="updatePendingState">
        UPDATE ai_pending_review t
        SET t.state_show = #{stateShow}, t.state_logic = #{stateLogic}
        WHERE t.id = #{pendingId}
    </update>

    <update id="updatePendingAllUserState">
        update ai_pending_review_user
        set state_show = #{stateShow}, state_logic = #{stateLogic}
        where todo_id= #{todoId}
    </update>

    <select id="queryPendingUserCount" resultType="java.lang.Integer">
        select count(1)
        from ai_pending_review_user
        where todo_id = #{todoId} AND state_logic = 0
    </select>

</mapper>
