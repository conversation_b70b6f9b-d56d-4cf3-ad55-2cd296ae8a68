app:
  id: agent_base
apollo:
  bootstrap:
    enabled: true
    namespaces: application,datasource,model,SKYLADDER.kafka-log
    eagerLoad:
      enabled: true
#  meta: http://10.124.192.118:8085
#  configService: http://10.172.147.46:30876


server:
  port: 8080
spring:
  application:
    name: kpiAssistant
  mvc:
    async:
      request-timeout: 600000  # 设置为600秒（10分钟）
  banner:
    charset: UTF-8
  jackson:
    time-zone: UTC
    date-format: "yyyy-MM-dd HH:mm:ss"
  log:
    kafka:
      enabled: false

