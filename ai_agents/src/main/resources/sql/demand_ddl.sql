CREATE TABLE `dm_search_vector` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                    `demand_id` varchar(64) NOT NULL comment '需求id',
                                    `demand_number` varchar(256)  DEFAULT NULL comment '需求编码',
                                    `main_keywords` varchar(2048) DEFAULT NULL  comment '一级关键词',
                                    `second_keywords` varchar(2048) DEFAULT NULL  comment '二级关键词',
                                    `summary` text DEFAULT NULL  comment 'ai生成一句话总结',
                                    `ai_summary_original_doc` text DEFAULT NULL comment 'ai生成总结',
                                    `main_vector` VECTOR(1024) DEFAULT NULL comment '一级关键词向量',
                                    `second_vector` VECTOR(1024) DEFAULT NULL comment '二级关键词向量',
                                    `summary_vector` VECTOR(1024) DEFAULT NULL comment '文本向量',
                                    `err_reason` text  DEFAULT NULL,
                                    `insert_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                    PRIMARY KEY (`id`),
                                    VECTO<PERSON> KEY `main_vector_idx` (`main_vector`) WITH (DISTANCE=INNER_PRODUCT, TYPE=HNSW_SQ),
                                    VECTOR KEY `second_vector_idx` (`second_vector`) WITH (DISTANCE=INNER_PRODUCT, TYPE=HNSW_SQ),
                                    VECTOR KEY `summary_vector_idx` (`summary_vector`) WITH (DISTANCE=INNER_PRODUCT, TYPE=HNSW_SQ)
) comment '需求搜索向量表(勿删除)'


alter table dm_info
    add is_vectored int default 0 null;