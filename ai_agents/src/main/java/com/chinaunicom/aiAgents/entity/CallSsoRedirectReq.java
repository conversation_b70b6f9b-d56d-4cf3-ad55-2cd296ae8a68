package com.chinaunicom.aiAgents.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

public class CallSsoRedirectReq {
    @JsonProperty("conversation_id")
    private String conversationId;
    @JsonProperty("chat_id")
    private String chatId;
    @JsonProperty("BPCAuthorization")
    private String bpcAuthorization;

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatId) {
        this.chatId = chatId;
    }

    public String getBpcAuthorization() {
        return bpcAuthorization;
    }

    public void setBpcAuthorization(String bpcAuthorization) {
        this.bpcAuthorization = bpcAuthorization;
    }
}
