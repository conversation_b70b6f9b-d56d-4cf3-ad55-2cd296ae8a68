package com.chinaunicom.aiAgents.controller;


import com.chinaunicom.aiAgents.components.contexts.Constants;
import com.chinaunicom.aiAgents.service.HttpClientService;
import com.chinaunicom.aiAgents.service.ProjectAssistantService;
import com.chinaunicom.common.entity.uniRequest.CommonAgentRequest;
import com.chinaunicom.common.entity.uniRequest.QueryInfo;
import com.chinaunicom.common.flow.ThinkingStage;
import com.chinaunicom.common.flow.context.DemandChatContext;
import com.chinaunicom.common.flow.context.InitProjectChatContext;
import com.chinaunicom.common.flow.context.MetricChatContext;
import com.chinaunicom.common.models.FakeSseServiceImpl;
import com.chinaunicom.common.models.TianTiSseServiceImpl;
import com.chinaunicom.common.models.tianti.TiantiResp;
import com.chinaunicom.common.utils.LogUtils;
import com.chinaunicom.common.utils.StringUtils;
import com.google.common.cache.LoadingCache;
import com.yomahub.liteflow.core.FlowExecutor;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.memory.ChatMemory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/agent/appExe/")
public class AgentController {

    @Autowired
    @Qualifier("CHAT_MEMORY_CACHE")
    LoadingCache<String, ChatMemory> chatMemoryCache;

    @Resource
    private FlowExecutor flowExecutor;


    @Resource
    ProjectAssistantService projectAssistantService;

    @Resource
    HttpClientService httpClientService;

    /*
     *
     * {
     *
     * historyInfo=[
     * {ai=1 + 4 + 1 = 6, human=再加上1是多少？, timestamp=1750302537},
     * {ai=2 * 6 = 12, human=2*6=？, timestamp=1750302511},
     * {ai="stroabarry"中有**3个字母"r"**。, human=stroabarry中有几个r, timestamp=1750302496},
     * {ai=1 + 4 = 5, human=1+4=？, timestamp=1750302460}
     * ],
     * queryInfo={
     * chat_id=0d06ro0Jhg0nuwgKU2VvC94WwWxnC5rG,
     * conversation_id=73b49e259bb14c648b9c8d7b534c1084,
     * query=1+3=？,
     * user_id=fbfb9a0ed92b4cdcb6976e502169b05e}
     * }
     * */


    /**
     * 效能助手入口
     * 目前暂定支持项目维度
     *
     * @param req
     * @return
     * @throws IOException
     */
    @PostMapping(value = "/chat/v2", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter aichatTest(@RequestBody CommonAgentRequest req) throws IOException {

        log.info("aisa 网关请求: {}", req);

        if (req.getQueryInfo().getConversationId() == null) {
            throw new RuntimeException("conversationId不能为空");
        }
        if (req.getQueryInfo().getUserId() == null) {
            throw new RuntimeException("userId不能为空");
        }

        //List<Map<String, String>> projectInfos = portalMapper.queryProjectByUserId(req.getQueryInfo().getUserId());
        List<Map<String, String>> projectInfos = new ArrayList<>();
        projectInfos.add(new HashMap<>() {{
            put("projectId", "9c9eeee9beb4437a8d59e408ae354e7e");
            put("projectName", "数字化研发平台项目");
        }});
//        if(projectInfos.isEmpty()){
//            //throw new RuntimeException("用户不属于任何项目");
//            projectInfos.add(new HashMap<>() {{
//                put("projectId", "9c9eeee9beb4437a8d59e408ae354e7e");
//                put("projectName", "数字化研发平台项目");
//            }});
//        }

        log.info("用户所属项目: {}, {}", projectInfos.get(0).get("projectId"),
                projectInfos.get(0).get("projectName"));

        SseEmitter sseEmitter = new SseEmitter();

        MetricChatContext metricChatContext = MetricChatContext.builder()
                .userMessage(new UserMessage(req.getQueryInfo().getQuery()))
                .isNeedReasoning(false)
                .ragSystemMessage(new ArrayList<>())
                .projectId(projectInfos.get(0).get("projectId"))
                .chatId(req.getQueryInfo().getChatId())
                .projectName(projectInfos.get(0).get("projectName"))
                //.chatMemory(chatMemoryCache.getUnchecked(req.getQueryInfo().getConversationId()))
                .conversationId(req.getQueryInfo().getConversationId())
                .sseService(new TianTiSseServiceImpl(sseEmitter))
                .build();

        /*添加用户问题*/
        // chatContext.getChatMemory().add(new UserMessage(req.getUserMsg()));

        flowExecutor.execute2FutureWithRid("metric-query-chat-chain", null, metricChatContext.getChatId(),
                metricChatContext);
        return sseEmitter;
    }


    /*
     *
     ** 场景一：PC 小研，
     *  1. 普通问答， 问题意图识别， 目前直接走智搜索
     *  2. 待办 需要思维链，默认问题  （附件，页面，项目历史数据）
     *
     *  pendingID 有，待办，无，普通问答
     *
     *
     * 场景二：web 悬浮伴随
     *   1. 普通问答， 问题意图识别，（附件，页面，项目历史数据），效果待定，默认问题待定
     *
     * */
    @PostMapping(value = "/chat/newDemand", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter aiNewDemand(@RequestBody CommonAgentRequest req) {

        log.info("需求助手 请求: {}", req);

        SseEmitter sseEmitter = new SseEmitter();

        if (req.getQueryInfo().getConversationId() == null) {
            throw new RuntimeException("conversationId不能为空");
        }

        if (req.getQueryInfo().getIsDemandFangtan() != null && req.getQueryInfo().getIsDemandFangtan()) {
            String conversationId = req.getQueryInfo().getConversationId();
            String chainName = "demand-interview-chat-chain"; // 默认单轮对话
            // 检查是否为多轮对话
            try {
                ChatMemory chatMemory = chatMemoryCache.getUnchecked(conversationId);
                List<SystemMessage> systemMessages = new ArrayList<>();
                for (dev.langchain4j.data.message.ChatMessage msg : chatMemory.messages()) {
                    if (msg instanceof SystemMessage) {
                        systemMessages.add((SystemMessage) msg);
                    }
                }
                // 如果有 SystemMessage，说明是多轮对话
                if (!systemMessages.isEmpty()) {
                    chainName = "demand-interview-summary-multi-chain";
                    log.info("检测到多轮对话，使用 chain: {}", chainName);
                } else {
                    log.info("首次对话，使用 chain: {}", chainName);
                }
            } catch (Exception e) {
                log.error("检查 chatMemory 失败，使用默认单轮对话", e);
            }
            DemandChatContext chatContext = DemandChatContext.builder()
                    .userMessage(new UserMessage(req.getQueryInfo().getQuery()))
                    .isNeedReasoning(false)
                    .conversationId(conversationId)
                    .chatId(req.getQueryInfo().getChatId())
                    .sseService(new TianTiSseServiceImpl(sseEmitter))
                    .build();
            flowExecutor.execute2FutureWithRid(chainName, null, chatContext.getChatId(), chatContext);
        } else {
            /*普通问答模式*/
            /*智搜模式*/
            //暂时将queryInfo里的所有信息传入extraParams，后续可以拓展
            DemandChatContext chatContext = DemandChatContext.builder()
                    .isNeedReasoning(false)
                    .userMessage(new UserMessage(req.getQueryInfo().getQuery()))
                    //.chatMemory(chatMemoryCache.getUnchecked(req.getQueryInfo().getConversationId()))
                    .conversationId(req.getQueryInfo().getConversationId())
                    .chatId(req.getQueryInfo().getChatId())
                    .sseService(new TianTiSseServiceImpl(sseEmitter))
                    .answerNote(new ArrayList<>())
                    .chatId(req.getQueryInfo().getChatId())
                    .backGroundInfo(req.getQueryInfo().getBackgroundInfo())
                    .build();
            if (req.getQueryInfo().getToolInfo() != null && StringUtils.isNotBlank(req.getQueryInfo().getToolInfo())) {
                flowExecutor.execute2FutureWithRid("demand-gongyan-chat-chain", null,
                        chatContext.getChatId(),
                        chatContext);
            } else {
                if (chatContext.getBackGroundInfo() == null ||
                        chatContext.getBackGroundInfo().get("BPCAuthorization") == null) {
                    throw new RuntimeException("背景信息或者BPCAuthorization鉴权不能为空");
                }
                String bpcAuthorization = (String) chatContext.getBackGroundInfo().get("BPCAuthorization");
                String token = httpClientService.getZhiSouTokenByOneStep(bpcAuthorization);

                chatContext.setToken(token);
                flowExecutor.execute2FutureWithRid("demand-zhisou-chat-chain", null,
                        chatContext.getChatId()
                        , chatContext);
            }
        }


        return sseEmitter;
    }


    /**
     * 附件，页面，项目历史数据
     * 立项助手V2,agent_id:01acd9a0e640576dbf4310ff285bbe50
     * 场景一：小研代办：    默认问题  思维链模式+思维链输出    背景知识：附件，页面，项目历史数据
     * 场景二：小研问答：    用户问题   非思维链模式，调text2sql流式接口，背景知识：项目历史数据
     * 场景三：专家审查：    默认问题   思维链模式+思维链输出    背景知识：附件，页面，项目历史数据
     * 场景四：项目经理自查： 默认问题   思维链模式+思维链输出    背景知识：附件，页面，项目历史数据
     * 场景五：专家问答：    用户问题   思维链模式但不输出思维链  背景知识：附件，页面，项目历史数据
     * 入参示例：
     * {
     * "historyInfo": [],
     * "queryInfo": {
     * "chat_id": "01acd9a0e640576dbf4310ff285bbe50",
     * "conversation_id": "73b49e259bb14c648b9c8d7b534c1084",
     * "user_id": "fbfb9a0ed92b4cdcb6976e502169b05e",
     * "query": "xxxxxxxxxx",
     * "backgroundInfo": {
     * 页面相关知识。。。。
     * "fileKeys": [],
     * "mode": "xmz",
     * "func": "capexPlanningStartCreate",
     * "act": "businessExperts"
     * }
     * }
     * }
     * <p>
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/chat/project", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter projectChat(@RequestBody CommonAgentRequest req) {
        log.info("aisa 网关projectChat请求: {}", req);

        QueryInfo queryInfo = req.getQueryInfo();

        //统一入参校验
        if (StringUtils.isBlank(queryInfo.getConversationId())) {
            throw new RuntimeException("conversationId不能为空");
        } else if (StringUtils.isBlank(queryInfo.getUserId())) {
            throw new RuntimeException("userId不能为空");
        } else if (StringUtils.isBlank(queryInfo.getQuery())) {
            throw new RuntimeException("query不能为空");
        } else if (StringUtils.isBlank(queryInfo.getChatId())) {
            throw new RuntimeException("chatId不能为空");
        }
        log.info("用户 ID: {}", queryInfo.getUserId());

        Map<String, Object> backgroundInfo = queryInfo.getBackgroundInfo();
        //定义一个场景标识,1-场景一， 2-场景二， 3-场景三， 4-场景四
        int sceneNo = 2; //如果场景识别失败，就默认场景二：小研立项助手问答

        //针对各种场景做场景特定入参校验
        if (ObjectUtils.isEmpty(backgroundInfo) && Constants.PROJECT_DEFAULT_QUERY.equals(queryInfo.getQuery())) {
            if (StringUtils.isBlank(queryInfo.getPendingId())) {
                throw new RuntimeException("pendingId不能为空"); //场景一小研代办必须传代办id
            }
            sceneNo = 1;//场景一：小研代办
        } else if (!ObjectUtils.isEmpty(backgroundInfo) && Constants.PROJECT_DEFAULT_QUERY.equals(queryInfo.getQuery())) {
            sceneNo = 3;//场景三：专家评审
            if (!backgroundInfo.containsKey("mode") || StringUtils.isBlank(MapUtils.getString(backgroundInfo, "mode"))) {
                backgroundInfo.put("mode", "xmz"); //立项助手的默认模块是xmz
            }
            if (!backgroundInfo.containsKey("func") || StringUtils.isBlank(MapUtils.getString(backgroundInfo, "func"))) {
                throw new RuntimeException("backgroundInfo.func不能为空");
            }
            if (!backgroundInfo.containsKey("act") || StringUtils.isBlank(MapUtils.getString(backgroundInfo, "act"))) {
                throw new RuntimeException("backgroundInfo.act不能为空");
            }
            if ("all".equals(MapUtils.getString(backgroundInfo, "act"))) {
                sceneNo = 4;//项目经理自查
            }
        } else if (!ObjectUtils.isEmpty(backgroundInfo) && !Constants.PROJECT_DEFAULT_QUERY.equals(queryInfo.getQuery())) {
            sceneNo = 5;//专家问答
        }

        SseEmitter sseEmitter = new SseEmitter();
        List<ThinkingStage> thinkingStages = projectAssistantService.queryRagInfo(sceneNo, queryInfo);
        /**这三种场景【小研代办，专家审查，项目经理自查】，默认问题，需要思维链，并输出思维链
         *  思维链：
         *   1、根据设定的规则，分析问题是否涉及页面信息，附件、历史数据
         *   2、涉及附件则查询rag接口，拿到相关文本text1
         *   3、涉及历史则text2sql接口，拿到相关文本text2
         *   4、涉及页面信息，调大模型提取页面知识
         *   5、将2,3,4获取的信息+prompt，给大模型生成最终答案
         *   6、将问答记录入项目制的库表
         */
        InitProjectChatContext chatContext = InitProjectChatContext.builder()
                .isNeedReasoning(false)
                .userId(queryInfo.getUserId())
                .conversationId(queryInfo.getConversationId())
                .chatId(queryInfo.getChatId())
                .thinkingStage(thinkingStages)
                .sseService(new TianTiSseServiceImpl(sseEmitter))
                .answerNote(new ArrayList<>())
                .build();
        // 1-小研代办，3-专家评审，4-项目经理自查
        if (sceneNo == 1 || sceneNo == 3 || sceneNo == 4) {
            flowExecutor.execute2FutureWithRid(Constants.PROJECT_CHAT_CHAIN, null,
                    chatContext.getChatId(), chatContext);
        } else if (sceneNo == 5) {
            // 5-专家问答
            flowExecutor.execute2FutureWithRid(Constants.PROJECT_CHAT_EXPERT, null,
                    chatContext.getChatId(), chatContext);
        } else {
            // 2-小研立项助手问答
            flowExecutor.execute2FutureWithRid(Constants.PROJECT_CHAT_XIAOYAN, null,
                    chatContext.getChatId(), chatContext);
        }
        return sseEmitter;
    }


    /*
     *
     * 项目经理自查，
     * 研发平台上立项流程页面上使用（核心）
     * */
    @PostMapping(value = "/check/project")
    public TiantiResp checkProjectChat(@RequestBody CommonAgentRequest req) {
        log.info("aisa 网关projectChat请求: {}", req);

        QueryInfo queryInfo = req.getQueryInfo();
        //统一入参校验
        if (StringUtils.isBlank(queryInfo.getConversationId())) {
            throw new RuntimeException("conversationId不能为空");
        } else if (StringUtils.isBlank(queryInfo.getUserId())) {
            throw new RuntimeException("userId不能为空");
        } else if (StringUtils.isBlank(queryInfo.getQuery())) {
            throw new RuntimeException("query不能为空");
        } else if (StringUtils.isBlank(queryInfo.getChatId())) {
            throw new RuntimeException("chatId不能为空");
        } else if (ObjectUtils.isEmpty(queryInfo.getBackgroundInfo())) {
            throw new RuntimeException("backgroundInfo不能为空");
        } else if (!queryInfo.getBackgroundInfo().containsKey("func") || StringUtils.isBlank(MapUtils.getString(queryInfo.getBackgroundInfo(), "func"))) {
            throw new RuntimeException("backgroundInfo.func不能为空");
        }
        Map<String, Object> backgroundInfo = queryInfo.getBackgroundInfo();
        //针对各种场景做场景特定入参校验
        if (!backgroundInfo.containsKey("mode") || StringUtils.isBlank(MapUtils.getString(backgroundInfo, "mode"))) {
            backgroundInfo.put("mode", "xmz"); //立项助手的默认模块是xmz
        }
        if (!backgroundInfo.containsKey("act") || StringUtils.isBlank(MapUtils.getString(backgroundInfo, "act"))) {
            backgroundInfo.put("act", "all");
        }

//        SseEmitter sseEmitter = new SseEmitter();
        List<ThinkingStage> thinkingStages = projectAssistantService.queryRagInfo(4, queryInfo);
        InitProjectChatContext chatContext = InitProjectChatContext.builder()
                .isNeedReasoning(false)
                .userId(queryInfo.getUserId())
                .conversationId(queryInfo.getConversationId())
                .chatId(queryInfo.getChatId())
                .thinkingStage(thinkingStages)
                .sseService(new FakeSseServiceImpl(null))
                .answerNote(new ArrayList<>())
                .build();
        flowExecutor.execute2FutureWithRid(Constants.PROJECT_CHAT_CHAIN, null,
                chatContext.getChatId(), chatContext);


        TiantiResp tiantiResp = new TiantiResp();
        tiantiResp.setCode(200);
        tiantiResp.setMessage("success");
        return tiantiResp;
    }
}
