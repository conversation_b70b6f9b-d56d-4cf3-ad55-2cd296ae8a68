package com.chinaunicom.aiAgents.controller;



import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinaunicom.aiAgents.dao.domain.DmSearchVector;
import com.chinaunicom.aiAgents.dao.mapper.DmSearchVectorMapper;

import com.chinaunicom.common.configuration.ChatModelInfos;
import com.chinaunicom.common.embedding.EmbeddingService;
import com.chinaunicom.common.flow.WaitingLock;
import com.chinaunicom.common.flow.context.BaseContext;
import com.chinaunicom.common.flow.context.InitProjectChatContext;
import com.chinaunicom.common.models.ChatRespSt;
import com.chinaunicom.common.models.ChatRespType;
import com.chinaunicom.common.utils.SseUtils;
import com.yomahub.liteflow.core.FlowExecutor;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.StreamingResponseHandler;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.output.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Controller
@RequestMapping("/inner/test")
@RequiredArgsConstructor
public class InnerTestController {


    @Resource
    private FlowExecutor flowExecutor;


    @Resource
    DmSearchVectorMapper dmSearchVectorMapper;


    @Autowired
    ChatModelInfos chatModelInfos;


    /**
     * 向量化文本内容
     */
    @GetMapping("/embeddingSearch")
    @ResponseBody
    public Object queryApplicantInfoByAssets(@RequestParam("demandNumber") String demandNumber,
                                             @RequestParam(value = "mainWeight", defaultValue = "0.5") Double mainWeight,
                                             @RequestParam(value = "secondWeight", defaultValue = "0.5") Double secondWeight) {
        List<DmSearchVector> dmSearchVectors = dmSearchVectorMapper.selectList(new LambdaQueryWrapper<DmSearchVector>()
                .eq(DmSearchVector::getDemandNumber, demandNumber));

        log.info("查询需求编号 : {}", dmSearchVectors.get(0).getDemandNumber());
//        log.info("查询需求编号 : {}", dmSearchVectors.get(0).getDemandNumber());
        List<Map<String, Object>> searchRes = dmSearchVectorMapper.queryByVector(
                dmSearchVectors.get(0).getMainVector(),
                dmSearchVectors.get(0).getSecondVector(), mainWeight, secondWeight);

        log.info("类似需求 : {}",
                searchRes.stream().map(v -> new JSONObject()
                                .fluentPut("需求编号", v.get("demand_number"))
                                .fluentPut("需求概括", v.get("ai_summary_original_doc").toString()))
                        .collect(Collectors.toList()));
        searchRes.forEach(v -> v.remove("ai_summary_original_doc"));
        return searchRes;
    }


    /*测试sse 客户端并发性能*/
    private static StreamingChatLanguageModel streamingChatLanguageModel = null;

    @PostMapping(value = "/testparrell", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ResponseBody
    public SseEmitter datasetGenerate() throws InterruptedException {

        if (streamingChatLanguageModel == null) {
            streamingChatLanguageModel = chatModelInfos.getStreamingLlm("deepseek-V3-tq-prod",
                    1.0, false);
        }

        SseEmitter sseEmitter = new SseEmitter();
        List<ChatMessage> chatMessages = new ArrayList<>();
        chatMessages.add(UserMessage.from("编造一个故事"));
        WaitingLock waitingLock = new WaitingLock();

        new Thread(() -> {
            streamingChatLanguageModel.generate(chatMessages, new StreamingResponseHandler<>() {
                @Override
                public void onNext(String token) {
                    try {
                        sseEmitter.send(String.format("""
                                {"data":"%s"}
                                """, token));
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }

                @Override
                public void onComplete(Response<AiMessage> response) {
                    sseEmitter.complete();
                    waitingLock.setFlag();
                }

                @Override
                public void onError(Throwable error) {
                    sseEmitter.complete();
                    waitingLock.setFlag();
                }
            });
            try {
                waitingLock.waitForFlag();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }).start();

        return sseEmitter;
    }


    @GetMapping(value = "/test", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter multiChat() {
        SseEmitter sseEmitter = new SseEmitter();

        flowExecutor.execute2Future("demo", null, sseEmitter);

        return sseEmitter;
    }

    @PostMapping(value = "/testLLM", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter testLLM(@RequestBody Map<String, Object> params) {
        SseEmitter sseEmitter = new SseEmitter();
        String question = MapUtils.getString(params, "question");

        ChatLanguageModel chatLanguageModel = chatModelInfos.getChatLlm("deepseek-V3-tq", 1.0, false);

        List<ChatMessage> chatMessages = new ArrayList<>();

        String sysPrompt = """
                ##核心任务
                    你的任务是根据用户的输入，提取关键信息并生成一个特定格式的JSON。
                    用户的输入包括【问题】和【校验规则】。
                    综合【问题】和【校验规则】从中识别查询数据来源，查询数据来源只有附件、页面信息、项目历史数据三种。
                
                ##任务要求
                    按照以下步骤操作：
                    1. 仔细阅读问题，定位关于查询数据来源的描述。
                    2. 若问题中提及的查询数据来源不在给定的三种之中，视为无效信息。
                    3. 将识别出的查询数据来源填入JSON中 "查询数据来源"，""查询数据信息"" 字段。
                    4. 查询数据信息指查询的具体字段名称
                    5. 若查询数据来源是"项目历史数据"，且字段名称中出现“本年” “上年”等字样，替换成25年/24年这样的对应年份
                    6. 若查询数据来源是"附件"，字段名称种出现“本年” “上年”等字样时，保留“本年”，“上年”等字样
                
                ##输出格式
                请在<json>标签内输出生成的JSON。
                <json>
                [
                    {
                        "查询数据来源": "页面信息",
                        "查询数据信息": ["三方投资信息"]
                    },
                    {
                        "查询数据来源": "附件",
                        "查询数据信息": ["自研信息","总投资额"]
                    },
                    {
                        "查询数据来源": "项目历史数据",
                        "查询数据信息": ["历史信息"]
                    }
                ]
                </json>
                """;
        chatMessages.add(SystemMessage.systemMessage(sysPrompt));
        chatMessages.add(UserMessage.from(question));

        Response<AiMessage> response = chatLanguageModel.generate(chatMessages);
        String ans = response.content().text();
        String trimmed = ans.replaceAll("<json>", "").replaceAll("</json>", "").trim();

        return sseEmitter;
    }

    @PostMapping(value = "/testRouter")
    public SseEmitter testRouter(@RequestBody Map<String, Object> params) {
        SseEmitter sseEmitter = new SseEmitter();
        String question = MapUtils.getString(params, "question");

        ChatLanguageModel chatLanguageModel = chatModelInfos.getChatLlm("deepseek-V3-tq", 1.0, false);

        List<ChatMessage> chatMessages = new ArrayList<>();

        String sysPrompt = """ 
                
                ##核心任务
                你的任务是判定用户输入的问题是否是一个通识类的普通问题。
                
                ##任务要求
                1、通识类的普通问题是指那些在大众普遍认知范围内，不涉及专业领域的高深知识、特定群体的小众话题、高度个性化或敏感的问题，通常是关于常见事物、现象、概念等具有广泛知晓度的询问。
                2、判定时，请仔细分析问题的内容，判断其是否符合上述通识类普通问题的定义。
                
                ##输出格式
                1、如果是通识类问题，请推理出答案，按如下格式在<json>标签内输出：
                    <json>
                        {
                            "是否通识类": "是",
                            "问题答案": [在此写下“问题的答案”]
                        }
                    </json>
                2、如果不是通识类问题，请直接输出下面<json>标签内的内容：
                    <json>
                        {
                            "是否通识类": "否" 
                        }
                    </json>
                """;
        chatMessages.add(SystemMessage.systemMessage(sysPrompt));
        chatMessages.add(UserMessage.from(question));

        Response<AiMessage> response = chatLanguageModel.generate(chatMessages);
        String ans = response.content().text();
        String trimmed = ans.replaceAll("<json>", "").replaceAll("</json>", "").trim();
        SseUtils.sendAiMsg(null, trimmed, ChatRespType.CONTENT, ChatRespSt.END);
        return sseEmitter;
    }

    @PostMapping(value = "/testAnalysisShow")
    public SseEmitter testAnalysisShow(@RequestBody Map<String, Object> params) {
        SseEmitter sseEmitter = new SseEmitter();
        String question = MapUtils.getString(params, "question");

        ChatLanguageModel chatLanguageModel = chatModelInfos.getChatLlm("deepseek-V3-tq", 1.0, false);

        List<ChatMessage> chatMessages = new ArrayList<>();

        String sysPrompt = """ 
            ##核心任务
                你的任务是根据用户的输入，分析用户的意图，给出解决这个问题的流程逻辑描述。
                用户的输入包括【问题】和【校验规则】。
        
            ##任务要求
                按照以下步骤操作：
                1. 仔细阅读用户输入的内容。
                2. 思考用户输入背后可能的意图，考虑输入的上下文、关键信息等。
                3. 基于分析出的意图，构建解决该问题的流程逻辑。流程逻辑应清晰、有条理，包含主要的步骤和决策点。
                4. 用户的问题都是检索数据然后做计算/展示/比较等操作。
                5. 若有【校验规则】部分，则只处理这部分的逻辑要求，不要发散
        
            ##输出格式
                先输出用户的原始输入
                再用简洁的语句按步骤描述用户的意图
            
            ##示例
            用户输入: "问题：附件中项目的建设内容、投资结构是什么?"
            输出:
            "问题: 附件中项目的建设内容、投资结构是什么?"
            1.从附件中查询【建设内容】
            2.从附件中查询【投资结构】
        
            """;

        chatMessages.add(SystemMessage.systemMessage(sysPrompt));
        chatMessages.add(UserMessage.from(question));

        Response<AiMessage> response = chatLanguageModel.generate(chatMessages);
        String ans = response.content().text();
        String trimmed = ans.replaceAll("<json>", "").replaceAll("</json>", "").trim();
        SseUtils.sendAiMsg(null, trimmed, ChatRespType.CONTENT, ChatRespSt.END);
        return sseEmitter;
    }

    @PostMapping(value = "/testAnalysisIsHis")
    public SseEmitter testAnalysisIsHis(@RequestBody Map<String, Object> params) {
        SseEmitter sseEmitter = new SseEmitter();
        String question = MapUtils.getString(params, "question");

        ChatLanguageModel chatLanguageModel = chatModelInfos.getChatLlm("deepseek-V3-tq", 1.0, true);

        List<ChatMessage> chatMessages = new ArrayList<>();

        String sysPrompt = """ 
            ##核心任务
                你的任务是根据用户输入的问题，判定是否需要查询历年历史信息，并返回是或否。
        
            ##任务要求
                按照以下步骤操作：
                1. 仔细阅读用户输入的内容。
                2. 如果问题中包含“上年”“历年”等明确指向历史信息的词汇，则判定为需要查询历年历史信息，返回“是”。
                3. 如果问题中包含“23年” “2022年” 这种词汇，需要和当前年份做比较，若早于今年，返回“是”。
                4. 如果问题中不包含上述词汇，则判定为不需要查询历年历史信息，返回“否”。
                5. 若判定为“是”, 则需要改写原始问题，将原始问题中的“本年” “上年”等字样，替换成25年/24年这样的对应年份。 “今年”“本年”不要替换。
   
            ##输出格式
                1、如果需要查询历年历史信息，请推理出答案，按如下格式在<json>标签内输出：
                    <json>
                        {
                            "是否需要查询历年": "是",
                            "改写后的问题":  [在此写下“改写后的问题”]
                        }
                    </json>
                2、如果不需要查询历年历史信息，请直接输出下面<json>标签内的内容：
                    <json>
                        {
                            "是否需要查询历年": "否"
                        }
                    </json>
            
            """;

        chatMessages.add(SystemMessage.systemMessage(sysPrompt));
        chatMessages.add(UserMessage.from(question));

        Response<AiMessage> response = chatLanguageModel.generate(chatMessages);
        String ans = response.content().text();
        String trimmed = ans.replaceAll("<json>", "").replaceAll("</json>", "").trim();
        SseUtils.sendAiMsg(null, trimmed, ChatRespType.CONTENT, ChatRespSt.END);
        return sseEmitter;
    }


}
