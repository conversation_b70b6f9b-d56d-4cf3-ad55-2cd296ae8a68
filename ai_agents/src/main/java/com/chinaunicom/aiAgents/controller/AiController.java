package com.chinaunicom.aiAgents.controller;


import com.google.common.cache.LoadingCache;
import com.chinaunicom.common.flow.context.MetricChatContext;
import com.chinaunicom.common.flow.WaitingLock;
import com.chinaunicom.aiAgents.dao.domain.UserChatLog;
import com.chinaunicom.aiAgents.dao.mapper.AiAgentConfigMapper;
import com.chinaunicom.aiAgents.dao.mapper.MetricMapper;

import com.chinaunicom.aiAgents.entity.ChatRequest;
import com.chinaunicom.common.models.TianTiSseServiceImpl;
import com.yomahub.liteflow.core.FlowExecutor;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.memory.ChatMemory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.ArrayList;


@Slf4j
@RestController
@RequestMapping("/api/")
public class AiController {

    @Resource
    private FlowExecutor flowExecutor;



    @Autowired
    @Qualifier("CHAT_MEMORY_CACHE")
    LoadingCache<String, ChatMemory> chatMemoryCache;


    @PostMapping(value = "/chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter aiChat(@RequestBody ChatRequest req) {
        if (req.getConversationId() == null) {
            throw new RuntimeException("conversationId不能为空");
        }

        SseEmitter sseEmitter = new SseEmitter();

        UserChatLog userChatLog = new UserChatLog();
        userChatLog.setConversationId(req.getConversationId());
        userChatLog.setQuestion(req.getUserMsg());
//        ChatMemory chatMemory = chatMemoryCache.getUnchecked(req.getConversationId());
//        chatMemory.messages() chatMemory.messages().stream().filter(v -> v.type() != ChatMessageType.USER).collect(Collectors.toList());
        MetricChatContext metricChatContext = MetricChatContext.builder()
                .userMessage(new UserMessage(req.getUserMsg()))
                .isNeedReasoning(req.getIsNeedThinking())
                .ragSystemMessage(new ArrayList<>())
                //.chatMemory(chatMemoryCache.getUnchecked(req.getConversationId()))
                .conversationId(req.getConversationId())
                .sseService(new TianTiSseServiceImpl(sseEmitter))
                .build();

        /*添加用户问题*/
        // chatContext.getChatMemory().add(new UserMessage(req.getUserMsg()));

        flowExecutor.execute2Future("metric-query-chat-new-chain", null, metricChatContext);
        return sseEmitter;
    }


    @PostMapping(value = "/chatTest", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter aichatTest(@RequestBody ChatRequest req) {

        SseEmitter sseEmitter = new SseEmitter();

        MetricChatContext metricChatContext = MetricChatContext.builder()
                .userMessage(new UserMessage(req.getUserMsg()))
                //.chatMemory(chatMemoryCache.getUnchecked(req.getConversationId()))
                .isNeedReasoning(req.getIsNeedThinking())
                .conversationId(req.getConversationId())
                .sseService(new TianTiSseServiceImpl(sseEmitter))
                .waitingLock(new WaitingLock())
                .build();


        // flowExecutor.execute2Future("qwen-chat-chain", null, chatContext);
        flowExecutor.execute2Future("metric-query-chat-new-chain", null, metricChatContext);
        return sseEmitter;
    }


    @Resource
    AiAgentConfigMapper aiAgentConfigMapper;

    @GetMapping(value = "/test")
    public Object multiChat() {

        //return aiAgentConfigMapper.selectOne(new QueryWrapper<AiAgentConfig>().eq("name", "立项助手"));
        return aiAgentConfigMapper.selectByAgentName("立项助手");

        //  return metricMapper.queryMetricUnitDefineALL();
    }


}
