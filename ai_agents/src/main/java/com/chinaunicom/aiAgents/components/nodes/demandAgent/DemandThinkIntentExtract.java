package com.chinaunicom.aiAgents.components.nodes.demandAgent;


import com.chinaunicom.common.configuration.ChatModelInfos;
import com.chinaunicom.common.flow.context.DemandChatContext;
import com.chinaunicom.common.flow.context.InitProjectChatContext;
import com.chinaunicom.common.flow.ThinkingStage;
import com.chinaunicom.common.models.ChatRespSt;
import com.chinaunicom.common.models.ChatRespType;
import com.chinaunicom.common.utils.SseUtils;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.output.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@LiteflowComponent("demandThinkIntentExtract")
public class DemandThinkIntentExtract extends NodeComponent {

    private final ChatLanguageModel chatLanguageModel;

    DemandThinkIntentExtract(@Autowired ChatModelInfos chatModelInfos) {
        super();
        this.chatLanguageModel = chatModelInfos.getChatLlm("deepseek-V3-tq-prod", 1.0, false);
    }


    private String getSystemPrompt() {
        return """
                你的任务是根据用户提出的问题，提取关键信息并生成一个特定格式的JSON。
                这是用户提出的问题：

                从问题中识别查询数据来源，查询数据来源只有附件、页面信息、项目历史数据三种。
                按照以下步骤操作：
                1. 仔细阅读问题，定位关于查询数据来源的描述。
                2. 若问题中提及的查询数据来源不在给定的三种之中，视为无效信息。
                3. 将识别出的查询数据来源填入JSON中 "查询数据来源"，""查询数据信息"" 字段。
                4. 查询数据信息指查询的具体内容
                                     
                请在<json>标签内输出生成的JSON。
                <json>
                [
                    {
                        "查询数据来源": "页面信息",
                        "查询数据信息": ["三方投资信息"]
                    },
                    {
                        "查询数据来源": "附件",
                        "查询数据信息": ["自研信息"]
                    }
                ]
                </json>
                    """;
    }

    @Override
    public void process() throws Exception {

        DemandChatContext chatContext = this.getContextBean(DemandChatContext.class);


        if (this.getCurrLoopObj() instanceof ThinkingStage thinkingStage) {

            List<ChatMessage> chatMessages = new ArrayList<>();
            chatMessages.add(SystemMessage.systemMessage(getSystemPrompt()));
            chatMessages.add(UserMessage.from(thinkingStage.getQuestion()));

            try {
                Response<AiMessage> response = chatLanguageModel.generate(chatMessages);
                String ans = response.content().text();
                String trimmed = ans.replaceAll("<json>", "")
                        .replaceAll("</json>", "")
                        .trim();
                // 使用 Gson 解析 JSON 字符串
                JsonArray jsonArray = JsonParser.parseString(trimmed).getAsJsonArray();

                // 获取 "待查询来源" 字段的值
                Map<String, List<String>> sources = new HashMap<>();
                jsonArray.asList().forEach(v -> {
                    JsonObject jsonObject = v.getAsJsonObject();
                    sources.put(jsonObject.get("查询数据来源").getAsString(),
                            jsonObject.get("查询数据信息").getAsJsonArray().asList().stream().map(JsonElement::getAsString).toList());
                });

                thinkingStage.getParallelLock().waitForFlag();
//                SseUtils.sendAiMsg(chatContext, "<br>", ChatRespType.CARD, ChatRespSt.START);

                thinkingStage.setSearchSources(getSearchSystemMessages(sources, ChatRespType.CARD));

            } catch (Exception e) {
                log.error("模型调用异常", e);
                //SseUtils.sendAiMsg(chatContext, "模型调用异常，请稍后再试", ChatRespType.error);
                SseUtils.sendAiMsg(chatContext, "模型调用异常，请稍后再试 " + e.getMessage(), ChatRespType.CARD, ChatRespSt.START);
            } finally {
                SseUtils.sendAiMsg(chatContext, "\n", ChatRespType.CARD, ChatRespSt.END);
            }
        } else {
            /* 异常情况处理 */
        }

    }

    public static void main(String[] args) {
        String ans = """
                [
                    {
                        "查询数据来源": "附件",
                        "查询数据信息": ["投资结构", "业务需求", "系统架构", "数据治理", "流程治理", "信创评估", "安全评估", "风险分析"]
                    }
                ]
                """;
        JsonArray jsonArray = JsonParser.parseString(ans).getAsJsonArray();

        // 获取 "待查询来源" 字段的值
        Map<String, List<String>> sources = new HashMap<>();
        jsonArray.asList().forEach(v -> {
            JsonObject jsonObject = v.getAsJsonObject();
            sources.put(jsonObject.get("查询数据来源").getAsString(),
                    jsonObject.get("查询数据信息").getAsJsonArray().asList().stream().map(JsonElement::getAsString).toList());
        });
        System.out.println(sources);
    }


    private List<SystemMessage> getSearchSystemMessages(Map<String, List<String>> sourceMaps, ChatRespType chatRespType) {
        DemandChatContext chatContext = this.getContextBean(DemandChatContext.class);
        List<String> cardList = new ArrayList<>();
        for (String k : sourceMaps.keySet()) {
            cardList.add(String.format("""
                    {"title":"%s","link":"https://zhihu.com"}
                    """, k));
        }
        for (int i = 0; i < 6; i++) {
            cardList.add(String.format("""
                    {"title":"%s","link":"https://zhihu.com"}
                    """, i));
        }


        SseUtils.sendAiMsg(chatContext, cardList.toString(), chatRespType, ChatRespSt.START);
        List<SystemMessage> systemMessages = new ArrayList<>();
        systemMessages.add(SystemMessage.systemMessage("""
                   附件内容：
                   项目投资结构：2025年项目规划总投资2601万元（较上年下降14.4%），第三方500完（较上年下降28.6%）
                   业务需求: 建设研发数智化能力：
                   1、建设需求助手：具备需求同质化分析能力，识别重复建设功能，辅助指导系统顶层设计
                   2、建设流水线助手：优化CI/CD流程，实现从代码提交到生产的全流程自动化，减少人工干预。利用AI进行流水线问题预警和自动修复，提前发现并解决潜在问题
                   3、建设代码安全智能检测能力：快速识别代码中的潜在问题与安全漏洞，提供高效的优化建议，从而保障项目的合规性与质量
                   4、提升小研助手能力：与客服进行底层融合统一，完成向量库替换；扩展知识文件支撑类型，可支持ppt、图片的文件格式；实现对pc/app客户端应用程序开发的技术积累，完成数智小研客户端开发，接入钉钉
                   5、接入DeepSeek开源模型
                                     
                """));
        return systemMessages;
    }


}
