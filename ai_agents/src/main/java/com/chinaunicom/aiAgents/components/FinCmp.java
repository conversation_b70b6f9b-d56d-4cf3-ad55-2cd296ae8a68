package com.chinaunicom.aiAgents.components;


import com.chinaunicom.common.flow.context.DemandChatContext;
import com.chinaunicom.common.flow.context.InitProjectChatContext;
import com.chinaunicom.common.flow.context.MetricChatContext;
import com.chinaunicom.common.models.ChatRespType;
import com.chinaunicom.common.utils.SseUtils;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.exception.NoSuchContextBeanException;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@LiteflowComponent("fin")
public class FinCmp<T> extends NodeComponent {


    @Override
    public void process() throws Exception {

        try {
            /*捕获之前流程运行的异常*/
            if (this.getSlot().getException() != null) {
                log.error("流程运行中发生异常 ", this.getSlot().getException());
//                SseUtils.sendAiMsg(chatContext,
//                        String.format("流程运行异常 %s", this.getSlot().getException().getMessage()),
//                        ChatRespType.NORMAL);
            }
        } catch (Exception e) {
//            log.error(" ", e);
        }
        try {
            MetricChatContext metricChatContext = this.getContextBean("chatContext");
            /*记录问题答复日志*/
//            userQuestionsLogMapper.insert(chatContext.getUserChatLog());
            metricChatContext.getSseService().sendFin();

            metricChatContext.getSseService().close();
        } catch (NoSuchContextBeanException noSuchContextBeanException) {
            log.warn("fin cmp no such context bean exception {}", noSuchContextBeanException.getMessage());
        }
        try {
            InitProjectChatContext chatContext = this.getContextBean(InitProjectChatContext.class);
            chatContext.getSseService().sendFin();

            chatContext.getSseService().close();
        } catch (NoSuchContextBeanException noSuchContextBeanException) {
            log.warn("fin cmp no such context bean exception {}", noSuchContextBeanException.getMessage());
        }

        try {
            DemandChatContext demandChatContext = this.getContextBean(DemandChatContext.class);
            /*记录问题答复日志*/
//            userQuestionsLogMapper.insert(chatContext.getUserChatLog());
            demandChatContext.getSseService().sendFin();

            demandChatContext.getSseService().close();
        } catch (NoSuchContextBeanException noSuchContextBeanException) {
            log.warn("fin cmp no such context bean exception {}", noSuchContextBeanException.getMessage());
        }


    }
}
