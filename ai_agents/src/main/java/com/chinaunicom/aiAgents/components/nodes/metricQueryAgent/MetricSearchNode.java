package com.chinaunicom.aiAgents.components.nodes.metricQueryAgent;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinaunicom.aiAgents.components.contexts.Constants;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.chinaunicom.aiAgents.components.nodes.common.ToolNode;
import com.chinaunicom.aiAgents.components.nodes.metricQueryAgent.entity.GraphData;
import com.chinaunicom.aiAgents.dao.domain.MetricUnitCatalogue;
import com.chinaunicom.aiAgents.dao.mapper.MetricMapper;
import com.chinaunicom.aiAgents.dao.mapper.MetricUnitCatalogueMapper;
import com.chinaunicom.common.flow.context.MetricChatContext;

import com.chinaunicom.common.flow.MetricContext;
import com.chinaunicom.common.models.ChatRespType;
import com.chinaunicom.common.utils.SseUtils;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.util.JsonUtil;
import dev.langchain4j.data.message.SystemMessage;
import lombok.extern.slf4j.Slf4j;


import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.chinaunicom.common.flow.MetricContext.MetricType.INDICATOR;
import static com.chinaunicom.common.flow.MetricContext.MetricType.METRIC_UNIT;


@Slf4j
@LiteflowComponent(Constants.NODE_TOOL_KPI_SEARCH)
public class MetricSearchNode extends ToolNode {

    private static final ObjectMapper objectMapper = new ObjectMapper();

//    private final static String projectId = "4d08a18476574b308c92dbefcd47642c";
//    private final static String projectName = "数据中台服务能力数据治理研发项目";

    @Resource
    MetricMapper metricMapper;
    @Resource
    MetricUnitCatalogueMapper metricUnitCatalogueMapper;

    private final Map<String, String> DIMENSION_TYPE = Map.ofEntries(
            Map.entry("1", "项目"),
            Map.entry("2", "平台"),
            Map.entry("3", "人员"),
            Map.entry("4", "组织机构"),
            Map.entry("6", "系统"),
            Map.entry("7", "生产单元"),
            Map.entry("8", "生产单元部门"),
            Map.entry("9", "生产单元分院"),
            Map.entry("10", "生产单元全院"),
            Map.entry("11", "流水线"),
            Map.entry("12", "省分"),
            Map.entry("13", "三级系统")
    );

    /**
     * 从表达式中提取所有以 METRIC_UNIT 开头的变量。
     *
     * @param expression
     * @return
     */
    private List<String> extractMetricUnits(String expression) {
        // 定义正则表达式模式，匹配以 METRIC_UNIT 开头的变量
        Pattern pattern = Pattern.compile("METRIC_UNIT_\\d+");
        Matcher matcher = pattern.matcher(expression);
        List<String> result = new ArrayList<>();

        // 查找所有匹配的变量
        while (matcher.find()) {
            result.add(matcher.group());
        }
        return result;
    }


    private SystemMessage getMetricSchema() {
        return SystemMessage.from("""
                metric_index_name    comment '指标名称',
                dimension_type       comment '指标维度',
                trend                comment '趋势标准，1越大越好，2越小也好，3越接近中位值越好',
                statistics_time      comment '统计时间',
                result               comment '指标结果',
                value                comment '指标数值',
                unit                 comment '指标单位',
                health               comment '健康度',
                if_unusual           comment '是否为异常数据 0正常 1异常',
                metric_index_formula      comment '指标的度量公式，指标是由一个或者多个度量元根据度量公式计算而来',
                metric_index_formula_desc comment '度量公式的详细描述',
                metric_unit_name     comment '度量元名称'
                metric_unit_id      comment '度量元的 ID，一般在metric_index_formula中出现的变量都为度量元'
                metric_unit_value   comment '度量元的值'
                metric_unit_unit    comment '度量元的单位'
                metric_unit_statistics_time  comment '度量元的计算账期'
                """);
    }


    /**
     * 查询指标相关定义信息
     *
     * @param kpiCode
     * @return
     */
    public List<SystemMessage> queryMetricCatalogues(String kpiCode, String projectId) {
        List<SystemMessage> systemMessages = new ArrayList<>();

        List<Map<String, String>> metricDefines = metricMapper.queryMetricDefine(kpiCode);
        metricDefines.forEach(v -> {
            String dimensionName = DIMENSION_TYPE.getOrDefault(v.get("dimension_type"), "未知维度");
            v.put("dimension_type", dimensionName);
//            log.info("{},{},{}");
        });
        List<String> metricUnits = metricDefines.stream()
                .map(v -> extractMetricUnits(v.get("metric_index_formula")))
                .flatMap(Collection::stream)
                .distinct()
                .toList();
        log.info("{} 相关的度量元 {}", kpiCode, metricDefines);
        try {
            systemMessages.add(SystemMessage.from("指标相关定义查询为：" +
                    objectMapper.writeValueAsString(metricDefines)));

            systemMessages.add(SystemMessage.from("指标查询结果为：" +
                    objectMapper.writeValueAsString(metricMapper.queryMetric(kpiCode, projectId))));

            if (!metricUnits.isEmpty()) {
                systemMessages.add(SystemMessage.from("度量值查询为：" +
                        objectMapper.writeValueAsString(metricMapper.queryMetricUnitDataByProject(projectId, metricUnits))));
            }
        } catch (JsonProcessingException e) {
            log.error("json 序列化异常", e);
        }


        return systemMessages;
    }

    @Override
    protected List<SystemMessage> toolProcess(MetricChatContext metricChatContext) {
        log.info("查询指标 {}", metricChatContext.getMetricContext());
        log.info("查询项目 {}", metricChatContext.getProjectName());
        String kpiCode = metricChatContext.getMetricContext().getKpiCode().getKey();
        MetricContext.MetricType metricType = metricChatContext.getMetricContext().getKpiCode().getValue();

        List<SystemMessage> toolSystemMessage = new ArrayList<>();
        toolSystemMessage.add(getMetricSchema());

        if (metricType == INDICATOR) {
            /*指标查询*/

            generateGraphData(metricChatContext, metricMapper.queryMetric(kpiCode, metricChatContext.getProjectId()));

            toolSystemMessage.addAll(queryMetricCatalogues(kpiCode, metricChatContext.getProjectId()));
        } else if (metricType == METRIC_UNIT) {
            /*度量元查询*/
            MetricUnitCatalogue metricUnitCatalogue = metricUnitCatalogueMapper.selectOne(
                    new LambdaQueryWrapper<MetricUnitCatalogue>()
                            .eq(MetricUnitCatalogue::getMetricUnitName, kpiCode)
                            .eq(MetricUnitCatalogue::getDimensionType, "1") // 暂定只考虑项目维度
                            .last("LIMIT 1"));
            String metricUnitRemark = String.format("%s的定义:%s",
                    metricUnitCatalogue.getMetricUnitName(), metricUnitCatalogue.getRemark());
            toolSystemMessage.add(SystemMessage.from(metricUnitRemark));
            try {
                List<Map<String, Object>> ragData = metricMapper.queryMetricUnitDataByProject(metricChatContext.getProjectId(),
                        Collections.singletonList(metricUnitCatalogue.getMetricUnitId()));
                generateGraphData(metricChatContext, ragData);
                toolSystemMessage.add(SystemMessage.from(metricUnitCatalogue.getMetricUnitName() + "查询结果为：" +
                        objectMapper.writeValueAsString(ragData)));
            } catch (JsonProcessingException e) {
                log.error("json 序列化异常", e);
            }

        }

        return toolSystemMessage;
    }


    /**
     * 生成画图数据
     *
     * @param metricChatContext
     */

    private void generateGraphData(MetricChatContext metricChatContext, List<Map<String, Object>> ragData) {
        String kpiCode = metricChatContext.getMetricContext().getKpiCode().getKey();
        MetricContext.MetricType metricType = metricChatContext.getMetricContext().getKpiCode().getValue();
        /*画图使用*/
        /*
        * {
              xAxis: {
                type: 'category',
                data: ['1', '2', '3', '4', '5', '6', '7']
              },
              yAxis: {
                type: 'value'
              },
              series: [
                {
                  data: [150, 230, 224, 218, 135, 147, 260],
                  type: 'line'
                }
              ]
            };
        * */
        if (metricType == INDICATOR) {
//            List<Map<String, Object>> res = metricMapper.queryMetric(kpiCode, projectId);
            if (!ragData.isEmpty()) {
                String metricIndexName = ragData.get(0).get("metric_index_name").toString();
                List<String> xAxis = ragData.stream().map(v -> v.get("statistics_time").toString()).toList();
                List<Float> yAxis = ragData.stream().map(v -> Float.parseFloat(v.get("value").toString())).toList();
                GraphData graphData = GraphData.builder()
                        .xNodes(xAxis)
                        .yNodes(yAxis)
                        .title(metricIndexName)
                        .type(GraphData.GRAPH_TYPE_LINE)
                        .build();
                //   SseUtils.sendAiMsg(metricChatContext, JsonUtil.toJsonString(graphData), ChatRespType.graphUsed);
            }
        } else if (metricType == METRIC_UNIT) {
//            List<Map<String, Object>> res = metricMapper.queryMetricUnitDataByProject(projectId, Collections.singletonList(kpiCode));
            if (!ragData.isEmpty()) {

                String metricUnitName = ragData.get(0).get("metric_unit_name").toString();
                List<String> xAxis = ragData.stream().map(v -> v.get("metric_unit_statistics_time").toString()).toList();
                List<Float> yAxis = ragData.stream().map(v -> Float.parseFloat(v.get("metric_unit_value").toString())).toList();
                GraphData graphData = GraphData.builder()
                        .xNodes(xAxis)
                        .yNodes(yAxis)
                        .title(metricUnitName)
                        .type(GraphData.GRAPH_TYPE_LINE)
                        .build();
                //  SseUtils.sendAiMsg(metricChatContext, JsonUtil.toJsonString(graphData), ChatRespType.graphUsed);
            }
        }

    }


}
