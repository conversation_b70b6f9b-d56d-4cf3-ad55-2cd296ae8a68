package com.chinaunicom.aiAgents.components.nodes.projectAgent;


import com.alibaba.fastjson2.JSONObject;
import com.chinaunicom.aiAgents.components.contexts.Constants;
import com.chinaunicom.aiAgents.dao.mapper.PortalMapper;

import com.chinaunicom.common.configuration.ChatModelInfos;
import com.chinaunicom.common.flow.ThinkingStage;
import com.chinaunicom.common.flow.context.InitProjectChatContext;
import com.chinaunicom.common.models.ChatRespSt;
import com.chinaunicom.common.models.ChatRespType;
import com.chinaunicom.common.utils.LogUtils;
import com.chinaunicom.common.utils.SseUtils;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeBooleanComponent;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.output.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 提取关键词
 */
@LiteflowComponent("analysisQueryType")
@Slf4j
public class AnalysisQueryTypeNode extends NodeBooleanComponent {

    @Resource
    PortalMapper portalMapper;

    private final ChatLanguageModel chatLanguageModel;

    AnalysisQueryTypeNode(@Autowired ChatModelInfos chatModelInfos) {
        super();
        this.chatLanguageModel = chatModelInfos.getChatLlm(Constants.PROJECT_LLM_NAME, 1.0, false);
    }


    private String getSystemPrompt() {
        List<Map<String, String>> prompt = portalMapper.queryPrompt("YuanJin", "analysisQueryType", "project");
        if (!prompt.isEmpty()) {
            return prompt.get(0).get("prompt_text");
        }
        return """ 
                
                ##核心任务
                你的任务是判定用户输入的问题是否是一个通识类的普通问题。
                
                ##任务要求
                1、通识类的普通问题是指那些在大众普遍认知范围内，不涉及专业领域的高深知识、特定群体的小众话题、高度个性化或敏感的问题，通常是关于常见事物、现象、概念等具有广泛知晓度的询问。
                2、判定时，请仔细分析问题的内容，判断其是否符合上述通识类普通问题的定义。
                
                ##输出格式
                1、如果是通识类问题，请推理出答案，按如下格式在<json>标签内输出：
                    <json>
                        {
                            "是否通识类": "是",
                            "问题答案": [在此写下“问题的答案”]
                        }
                    </json>
                2、如果不是通识类问题，请直接输出下面<json>标签内的内容：
                    <json>
                        {
                            "是否通识类": "否" 
                        }
                    </json>
                """;
    }

    @Override
    public boolean processBoolean() {
        InitProjectChatContext projectChatContext = this.getContextBean(InitProjectChatContext.class);
        List<ChatMessage> chatMessages = new ArrayList<>();
        chatMessages.add(SystemMessage.systemMessage(getSystemPrompt()));
        if (this.getCurrLoopObj() instanceof ThinkingStage thinkingStage) {
            chatMessages.add(SystemMessage.systemMessage(thinkingStage.getQuestion()));
            try {
                Response<AiMessage> response = chatLanguageModel.generate(chatMessages);
                String ans = response.content().text();
                log.info("ans {}", ans);
                String trimmed = ans.replaceAll("<json>", "").replaceAll("</json>", "").trim();
                JSONObject json = JSONObject.parseObject(trimmed);
                if ("否".equals(json.getString("是否通识类"))) {
                    SseUtils.sendAiMsg(projectChatContext, "是否通识类问题分析", ChatRespType.ANALYSIS, ChatRespSt.START);
                    return true;
                } else if ("是".equals(json.getString("是否通识类"))) {
                    return false;
                }
            } catch (Exception e) {
                log.error("模型调用异常", e);
                SseUtils.sendAiMsg(projectChatContext, "模型调用异常，请稍后再试", ChatRespType.error);
                return false;
            }
            return false;
        }
        return false;
    }
}
