package com.chinaunicom.aiAgents.components.nodes.metricQueryAgent;


import com.chinaunicom.aiAgents.components.contexts.Constants;
import com.chinaunicom.aiAgents.components.nodes.common.ChatNode;
import com.chinaunicom.common.configuration.ChatModelInfos;
import com.chinaunicom.common.flow.context.MetricChatContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import dev.langchain4j.data.message.SystemMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
@LiteflowComponent(Constants.NODE_KPI_CHAT)
public class FinalChatNode extends ChatNode {


    FinalChatNode(@Autowired ChatModelInfos chatModelInfos) {
        super(chatModelInfos.getStreamingLlm("deepseek-V3-tq"));
    }


    @Override
    protected List<SystemMessage> getRagSystemMessages() {

        return this.getContextBean(MetricChatContext.class).getRagSystemMessage();
    }


    @Override
    protected String getSystemPrompt() {
        String SYSTEM_PROMPT = """
                你是一个指标查询助手，你的任务是根据提供的上下文信息，简洁地帮助用户回答问题。在回答时，若上下文中有相关信息，必须给出指标明确的定义和计算方法，然后提炼出简洁的答案；
                若上下文中没有相关信息，不要自己编造数据，而是明确告知用户无相关数据。
                            
                请按照以下步骤处理：
                1. 仔细阅读上下文信息和问题。
                2. 如果找到相关信息，给出指标明确的定义和计算方法。
                3. 根据指标定义和计算方法，提炼出简洁的答案。
                4. 如果未找到相关信息，
                """;
        return SYSTEM_PROMPT;
    }
}
