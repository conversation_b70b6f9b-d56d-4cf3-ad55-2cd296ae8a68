package com.chinaunicom.aiAgents.components.nodes.projectAgent;


import com.alibaba.fastjson2.JSONObject;
import com.chinaunicom.common.flow.ProjectQueryInfo;
import com.chinaunicom.common.flow.ThinkingStage;
import com.chinaunicom.common.flow.WaitingLock;
import com.chinaunicom.common.flow.context.InitProjectChatContext;
import com.chinaunicom.common.models.ChatRespSt;
import com.chinaunicom.common.models.ChatRespType;
import com.chinaunicom.common.sseClient.RagApi;
import com.chinaunicom.common.sseClient.SseHandler;
import com.chinaunicom.common.sseClient.ThirdSseClientConfig;
import com.chinaunicom.common.utils.SseUtils;
import com.chinaunicom.common.utils.StringUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import dev.langchain4j.data.message.SystemMessage;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.apache.commons.collections.MapUtils;
import retrofit2.Call;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fasterxml.jackson.databind.SerializationFeature.INDENT_OUTPUT;

/**
 * 小研问答专业问题
 */
@LiteflowComponent("text2sqlExtract")
@Slf4j
public class Text2sqlExtractNode extends NodeComponent {

    @Resource
    RagApi ragApi;
    @Resource
    OkHttpClient okHttpClient;

    @Override
    public void process() throws Exception {
        InitProjectChatContext chatContext = this.getContextBean(InitProjectChatContext.class);
        if (this.getCurrLoopObj() instanceof ThinkingStage thinkingStage) {
            try {
                thinkingStage.getProjectQueryInfo().setLlmQuery(thinkingStage.getProjectQueryInfo().getQuery());
                thinkingStage.getParallelLock().waitForFlag();
                thinkingStage.setSearchSources(getSearchSystemMessages(chatContext, thinkingStage, ChatRespType.CARD));
            } catch (Exception e) {
                log.error("模型调用异常", e);
                //SseUtils.sendAiMsg(chatContext, "模型调用异常，请稍后再试", ChatRespType.error);
                SseUtils.sendAiMsg(chatContext, "模型调用异常，请稍后再试 " + e.getMessage(), ChatRespType.CARD, ChatRespSt.START);
            } finally {
                SseUtils.sendAiMsg(chatContext, "\n", ChatRespType.CARD, ChatRespSt.END);
            }
        } else {
            /* 异常情况处理 */
        }

    }

    public static void main(String[] args) {

    }


    private List<SystemMessage> getSearchSystemMessages(InitProjectChatContext chatContext, ThinkingStage thinkingStage, ChatRespType chatRespType) {
        ProjectQueryInfo queryInfo = thinkingStage.getProjectQueryInfo();
        Map<String, String> bgMap = new HashMap();
        List<SystemMessage> systemMessages = new ArrayList<>();
        List<String> cardList = new ArrayList<>();
        //组装项目历史知识
        cardList.add(String.format("""
                {"title":"查询历史数据","link":"https://text2sql.com"}
                """));
        String text = getContentFromText2Sql(queryInfo);
        systemMessages.add(SystemMessage.systemMessage("项目历史数据：" + text));
        bgMap.put("项目历史数据", text);

        SseUtils.sendAiMsg(chatContext, cardList.toString(), chatRespType, ChatRespSt.START);
//        queryInfo.setBackgroundInfo(JSONObject.toJSONString(bgMap));
        return systemMessages;
    }


    /**
     * 调用谢兆伟的text2sql接口获取数据
     * 测试环境：http://10.168.32.90:31258/text2sql/external/api
     * 生产环境：http://10.168.32.85:31920/text2sql/external/api
     * POST请求：
     * application/json
     * <p>
     * {"request_id":"12321232",  --请求id  {request.user_id}_{request.chat_id}_{random.randint(1000, 9999)}
     * "router":"project_data",   --固定值
     * "query":"数字化研发平台在2024年自研投资占比有没有超过50%"  --用户问题
     * }
     */
    public String getContentFromText2Sql(ProjectQueryInfo queryInfo) {
        log.info("queryInfo {}", queryInfo);
        StringBuffer sb = new StringBuffer();
        Map<String, String> params = new HashMap();
        params.put("user_id", queryInfo.getUserId());
        params.put("chat_id", queryInfo.getChatId());
        params.put("query", queryInfo.getLlmQuery());
        InitProjectChatContext chatContext = this.getContextBean(InitProjectChatContext.class);
        WaitingLock waitingLock = new WaitingLock();
        EventSourceListener eventSourceListener = ThirdSseClientConfig.buildEventSourceListener(new SseHandler() {
            String think = "";

            @Override
            public void onNext(String token) {
                try {
                    if (StringUtils.isJsonString(token)) {
                        Map map = JSONObject.parseObject(token, Map.class);
                        String content = MapUtils.getString(map, "content");
                        think = MapUtils.getString(map, "think").replace(think, "");
                        SseUtils.sendAiMsg(chatContext, think, ChatRespType.CARD, ChatRespSt.START);
                        sb.append(content);
                    }
                } catch (Exception e) {
                    log.error("", e);
                }
            }

            @Override
            public void onComplete() {
                waitingLock.setFlag();
            }

            @Override
            public void onError(Throwable error) {
                waitingLock.setFlag();
                log.error(error.getMessage());
            }
        });

        try {
            ObjectMapper OBJECT_MAPPER = new ObjectMapper().enable(INDENT_OUTPUT);
            JsonNode jsonNode = OBJECT_MAPPER.readTree(JSONObject.toJSONString(params));
            Call<Map<String, Object>> call = ragApi.text2Sql(jsonNode);
            EventSources.createFactory(okHttpClient).newEventSource(call.request(), eventSourceListener);
            waitingLock.waitForFlag();
            log.info("调用 getContentFromText2Sql 接口，响应内容:{}", sb);
            return sb.toString();
        } catch (Exception e) {
            log.error("调用 getContentFromText2Sql 接口，错误信息:{}", e);
            return null;
        }
    }
}
