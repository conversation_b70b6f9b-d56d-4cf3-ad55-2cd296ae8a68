package com.chinaunicom.aiAgents.components.nodes;


import com.chinaunicom.common.entity.TQXYRequest;
import com.chinaunicom.common.models.tianqAiModel.TQCompletionRequest;
import com.chinaunicom.common.models.tianti.TiantiRequest;
import com.chinaunicom.common.models.tianti.TiantiResp;
import com.chinaunicom.common.sseClient.*;
import com.chinaunicom.common.utils.TqApiHelper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.chinaunicom.common.flow.WaitingLock;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import retrofit2.Call;

import javax.annotation.Resource;
import java.util.Map;

import static com.fasterxml.jackson.databind.SerializationFeature.INDENT_OUTPUT;

@Slf4j
@LiteflowComponent("demo")
public class DemoNode extends NodeComponent {


    @Override
    public void process() throws Exception {
        SseEmitter sseEmitter = this.getContextBean(SseEmitter.class);


        throw new RuntimeException("not support");


    }
}
