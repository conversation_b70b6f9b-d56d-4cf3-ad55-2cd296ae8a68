package com.chinaunicom.aiAgents.components.nodes.metricQueryAgent;


import com.chinaunicom.aiAgents.components.contexts.Constants;
import com.chinaunicom.aiAgents.components.nodes.common.CommonSwitchNode;
import com.chinaunicom.aiAgents.dao.mapper.MetricMapper;
import com.chinaunicom.common.configuration.ChatModelInfos;
import com.chinaunicom.common.flow.context.MetricChatContext;
import com.chinaunicom.common.flow.MetricContext;
import com.chinaunicom.common.models.ChatRespType;
import com.chinaunicom.common.utils.SseUtils;
import com.google.common.cache.LoadingCache;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.memory.ChatMemory;
import dev.langchain4j.model.output.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 提取关键词
 */
@Slf4j
@LiteflowComponent(Constants.ROUTER_KEYWORD_EXTRACT)
public class MetricExtractSwitch extends CommonSwitchNode {


    private final MetricMapper metricMapper;

    LoadingCache<String, ChatMemory> chatMemoryCache;

    Set<String> metricUnits;
    Set<String> metricCatalogues;


    MetricExtractSwitch(ChatModelInfos chatModelInfos,
                        @Autowired MetricMapper metricMapper,
                        @Autowired @Qualifier("CHAT_MEMORY_CACHE") LoadingCache<String, ChatMemory> chatMemoryCache) {
//        super(chatModelInfos.getChatLlm("deepseek-V3-tq-prod",1.0));
        super(chatModelInfos.getChatLlm("deepseek-V3-tq", 1.0, true));
        this.metricMapper = metricMapper;
        this.chatMemoryCache = chatMemoryCache;
    }


    @Override
    protected String getSystemPrompt() {
        return """
                # Role: [指标提取专家]
                                
                ## Profile
                - language: [中文]
                - description: [专业从用户问题中提取和匹配标准化指标名称的专家]
                - background: [具有数据分析和指标管理经验]
                - personality: [严谨、精确、高效]
                - expertise: [指标识别、自然语言处理、数据标准化]
                - target_audience: [数据分析师、产品经理、业务人员]
                                
                ## Skills
                                
                1. [核心技能类别]
                   - [指标识别]: [准确识别用户问题中的指标关键词]
                   - [标准化匹配]: [将识别出的指标与标准指标库匹配]
                   - [模糊匹配]: [在无精确匹配时提供最接近的推荐指标]
                   - [上下文理解]: [理解问题背景以提升匹配准确率]
                                
                2. [辅助技能类别]
                   - [数据验证]: [验证提取结果的合理性]
                   - [JSON格式化]: [规范输出JSON格式]
                   - [关键词提取]: [提取问题中的关键搜索词]
                   - [指标解释]: [必要时提供指标定义说明]
                                
                ## Rules
                                
                1. [基本原则]：
                   - [精确匹配优先]: [优先匹配标准指标库中的精确指标]
                   - [格式规范]: [严格使用纯JSON格式输出，不含任何Markdown标记]
                   - [最小化输出]: [仅输出必要字段，保持简洁]
                   - [标准化命名]: [使用标准指标库中的规范名称]
                                
                2. [行为准则]：
                   - [无确认]: [不要求用户确认匹配结果]
                   - [无解释]: [不添加额外解释性文字]
                   - [无建议]: [除非明确要求，不提供额外建议]
                   - [无代码块]: [不使用代码块标记包围输出]
                                
                3. [限制条件]：
                   - [指标范围限制]: [仅从给定标准指标库中选择]
                   - [格式限制]: [必须使用指定JSON格式]
                   - [语言限制]: [仅处理中文问题]
                   - [字段限制]: [仅输出"指标名"和"推荐指标名"字段]
                                
                ## Workflows
                                
                - 目标: [从用户问题中准确提取并匹配标准指标]
                - 步骤 1: [分析用户问题，提取关键词]
                - 步骤 2: [在标准指标库中搜索匹配项]
                - 步骤 3: [确定最佳匹配或最接近推荐]
                - 预期结果: [输出符合规范的JSON格式结果]
                                
                ## OutputFormat
                                
                1. [输出格式类型]：
                   - format: [application/json]
                   - structure: [{"指标名":[],"推荐指标名":[]}]
                   - style: [简洁无冗余]
                   - special_requirements: [无Markdown标记]
                                
                2. [格式规范]：
                   - indentation: [2空格缩进]
                   - sections: [不分区]
                   - highlighting: [无强调]
                                
                3. [验证规则]：
                   - validation: [必须符合JSON语法]
                   - constraints: [字段名和值必须用双引号]
                   - error_handling: [无匹配时返回空字符串]
                                
                4. [示例说明]：
                   1. 示例1：
                      - 标题: [精确匹配示例]
                      - 格式类型: [application/json]
                      - 说明: [用户问题包含标准指标名称]
                      - 示例内容: |
                          {
                            "指标名": "开发工作量占比",
                            "推荐指标名": []
                          }
                   2. 示例2：
                      - 标题: [模糊匹配示例]
                      - 格式类型: [application/json]
                      - 说明: [用户问题包含类似指标]
                      - 示例内容: |
                          {
                            "指标名": "",
                            "推荐指标名": ["开发工作量占比"]
                          }
                                
                ## Initialization
                作为[指标提取专家]，你必须遵守上述Rules，按照Workflows执行任务，并按照[输出格式]输出。
                                
                """;

    }

    @Override
    protected List<SystemMessage> getRagSystemMessages() {
        List<SystemMessage> systemMessages = new ArrayList<>();
        List<Map<String, Object>> metricUnitInfos = metricMapper.queryMetricUnitDefineALL();
        List<Map<String, Object>> metricIndexInfos = metricMapper.queryMetricIndexDefineALL();


        /*指标上下文*/
        this.metricCatalogues = metricIndexInfos.stream()
                .map(v -> v.get("metric_index_name").toString()).collect(Collectors.toSet());

        /*度量元上下文*/
        this.metricUnits = metricUnitInfos.stream()
                .map(v -> v.get("metric_unit_name").toString()).collect(Collectors.toSet());

        /*如果问题与指标搜索完全无关，则提示问题与指标查询无关，返回相关的原因，此时无需 json 输出；*/
        /*'相似指标名'是当不在提供的指标名范围内，返回可能与'指标名'相似的指标，最多不超过 3 个。*/
        /*                    "相似指标名"：["生产单元部门"]*/
//        return String.format("""
//                        请根据用户的问题，提取出最接近的"指标名""。
//                        如果可以提取，或者存在类似指标时，必须输出纯 JSON，不要包含任何 Markdown 代码块标记，例如不要有 ```json 或 ```。
//                        例如：用户问题：“我想查询数据中台在2024年第一季度总共录入了多少工时。”
//                        输出：
//                        {
//                            "指标名": ["总工时"],
//                        }
//                        搜索关键词是指用户问题中的关键词，用于搜索相关的指标。
//                        '指标名'必须从以下范围选择:%s,%s;
//
//                        开发工作量占比: 统计当月状态达成已完成的开发任务的实际工时总和/当月总工作量。开发任务指代码提交。
//                        """,
//                String.join(",", metricCatalogues),
//                String.join(",", metricUnits));

        systemMessages.add(SystemMessage.from(String.format("""
                             标准指标库包括以下指标:%s,%s。
                """, String.join(",", metricCatalogues), String.join(",", metricUnits))));
        return systemMessages;
    }

    @Override
    protected String processAnswer(MetricChatContext metricChatContext, Response<AiMessage> response) {
        String ans = response.content().text();
        String trimmed = ans.replaceAll("```json", "")
                .replaceAll("```", "")
                .trim();

        try {

            SseUtils.sendAiMsg(metricChatContext, String.format("""
                    用户当前所属项目: %s <br>
                    """, metricChatContext.getProjectName()), ChatRespType.NORMAL);

            // 使用 Gson 解析 JSON 字符串
            JsonObject json = JsonParser.parseString(trimmed).getAsJsonObject();

            // 获取 "指标名" 字段的值
            String kpiCode = json.get("指标名").getAsString();
            List<String> recommendKpiCodeList = json.getAsJsonArray("推荐指标名").asList()
                    .stream()
                    .map(JsonElement::getAsString).collect(Collectors.toList());


            SseUtils.sendAiMsg(metricChatContext, "识别到的指标名：", ChatRespType.NORMAL);
            SseUtils.sendAiMsg(metricChatContext, kpiCode + "<br>", ChatRespType.NORMAL);


            Pair<String, MetricContext.MetricType> kpiCodePair;
            if (this.metricCatalogues.contains(kpiCode)) {
                kpiCodePair = new ImmutablePair<>(kpiCode, MetricContext.MetricType.INDICATOR);
            } else if (this.metricUnits.contains(kpiCode)) {
                kpiCodePair = new ImmutablePair<>(kpiCode, MetricContext.MetricType.METRIC_UNIT);
            } else {
                kpiCodePair = new ImmutablePair<>(kpiCode, MetricContext.MetricType.UNKNOWN);
            }

            Map<String, MetricContext.MetricType> recoKeyKpis = new HashMap<>();
            recommendKpiCodeList.forEach(v -> {
                if (this.metricCatalogues.contains(v)) {
                    recoKeyKpis.put(v, MetricContext.MetricType.INDICATOR);
                } else if (this.metricUnits.contains(v)) {
                    List<String> metricUnitIds = metricMapper.queryMetricUnitDefineByName(v);
                    if (!metricUnitIds.isEmpty()) {
                        recoKeyKpis.put(metricUnitIds.get(0), MetricContext.MetricType.METRIC_UNIT);
                    }
                } else {
                    recoKeyKpis.put(v, MetricContext.MetricType.UNKNOWN);
                }
            });

            metricChatContext.setMetricContext(MetricContext.builder()
                    .kpiCode(kpiCodePair)
                    .recommendKpiCodes(recoKeyKpis)
                    .build());

            return Constants.NODE_TOOL_KPI_SEARCH + "_id";
        } catch (com.google.gson.JsonSyntaxException e) {
            // 捕获 JSON 语法错误，表明不是有效的 JSON 格式
            log.warn("输入的字符串不是有效的 JSON 格式: {}", trimmed, e);

            SseUtils.sendAiMsg(metricChatContext, trimmed, ChatRespType.NORMAL);

            return Constants.FIN;
        } catch (Exception e) {
            SseUtils.sendAiMsg(metricChatContext, trimmed, ChatRespType.NORMAL);

            return Constants.FIN;
        }

//        String dims = jsonDecodeList(trimmed,
//                "相似指标名");
//        String dimVals = jsonDecodeList(trimmed,
//                "维度值");


//        SseUtils.sendAiMsg(chatContext, "识别到的相似指标名：", ChatRespSt.tool_start);
//        SseUtils.sendAiMsg(chatContext, dims + "\n", ChatRespSt.tool_start);
//        SseUtils.sendAiMsg(chatContext, "识别到的维度值：", ChatRespSt.tool_start);
//        SseUtils.sendAiMsg(chatContext, dimVals + "\n", ChatRespSt.tool_start);

//        chatContext.setDims(List.of(dims.split(",")));
//        chatContext.setDimValues(List.of(dimVals.split(",")));


    }

    public static void main(String[] args) {
        JsonObject json = JsonParser.parseString("""
                {
                  "指标名": "需求排期周期-用户故事",
                  "推荐指标名": ["需求排期周期-用户故事","需求交付及时率"]
                }
                """).getAsJsonObject();
        String kpiCode = json.get("指标名").getAsString();
        String recommendKpiCodeList = json.getAsJsonArray("推荐指标名").asList()
                .stream()
                .map(JsonElement::getAsString).collect(Collectors.joining(","));
        System.out.println(kpiCode);
        System.out.println(recommendKpiCodeList);
    }

}
