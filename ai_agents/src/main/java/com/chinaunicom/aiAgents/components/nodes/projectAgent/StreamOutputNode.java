package com.chinaunicom.aiAgents.components.nodes.projectAgent;


import com.chinaunicom.aiAgents.components.contexts.Constants;
import com.chinaunicom.aiAgents.dao.mapper.PortalMapper;

import com.chinaunicom.aiAgents.service.impl.ProjectServicePlus;
import com.chinaunicom.common.configuration.ChatModelInfos;
import com.chinaunicom.common.flow.ProjectQueryInfo;
import com.chinaunicom.common.flow.ThinkingStage;
import com.chinaunicom.common.flow.WaitingLock;
import com.chinaunicom.common.flow.context.InitProjectChatContext;
import com.chinaunicom.common.models.ChatRespSt;
import com.chinaunicom.common.models.ChatRespType;
import com.chinaunicom.common.utils.LogUtils;
import com.chinaunicom.common.utils.SseUtils;
import com.chinaunicom.common.utils.StringUtils;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.StreamingResponseHandler;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.output.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@LiteflowComponent("streamOutput")
@Slf4j
public class StreamOutputNode extends NodeComponent {

    @Resource
    ProjectServicePlus projectServicePlus;
    @Resource
    PortalMapper portalMapper;
    StreamingChatLanguageModel streamingChatLanguageModel;

    StreamOutputNode(@Autowired ChatModelInfos chatModelInfos) {
        super();
        streamingChatLanguageModel = chatModelInfos.getStreamingLlm(Constants.PROJECT_LLM_NAME, 0.7, true);
    }

    @Override
    public void process() throws Exception {
        WaitingLock waitingLock = new WaitingLock();
        InitProjectChatContext chatContext = this.getContextBean(InitProjectChatContext.class);
        int queryNum = chatContext.getThinkingStage().size();
        if (this.getCurrLoopObj() instanceof ThinkingStage thinkingStage) {
            int progressRate = (this.getLoopIndex() + 1) * 100 / queryNum;
            // 获取循环上下文
            List<ChatMessage> chatMessages = new ArrayList<>();
            chatMessages.add(UserMessage.from(thinkingStage.getQuestion()));
            chatMessages.add(new SystemMessage(getSystemPrompt()));

            /*占位，保持状态开始结束*/
            SseUtils.sendAiMsg(chatContext, "", ChatRespType.NORMAL, ChatRespSt.START);
            streamingChatLanguageModel.generate(chatMessages, new StreamingResponseHandler<>() {
                @Override
                public void onNext(String token) {
                    SseUtils.sendAiMsg(chatContext, token, ChatRespType.NORMAL, ChatRespSt.START);
                }

                @Override
                public void onComplete(Response<AiMessage> response) {
                    log.info("onComplete {}", response.content());
                    chatContext.getAnswerNote().add(response.content().text());
                    chatContext.setProgressRate(progressRate);
                    ProjectQueryInfo queryInfo = thinkingStage.getProjectQueryInfo();
                    queryInfo.setFinalAnswer(response.content().text());
                    queryInfo.setConversationId(chatContext.getConversationId());
                    queryInfo.setChatId(chatContext.getChatId());
                    queryInfo.setProgressRate(progressRate);
                    projectServicePlus.syncChatHistory2Xmz(List.of(queryInfo));
                    SseUtils.sendAiMsg(chatContext, "\n", ChatRespType.NORMAL, ChatRespSt.END);
                    waitingLock.setFlag();
                }

                @Override
                public void onError(Throwable error) {
                    SseUtils.sendAiMsg(chatContext, "\n 模型响应异常 \n" + error.getMessage(), ChatRespType.NORMAL, ChatRespSt.END);
                    waitingLock.setFlag();
                }

            });
            waitingLock.waitForFlag();
        }
    }

    protected String getSystemPrompt() {
        List<Map<String, String>> prompt = portalMapper.queryPrompt("YuanJin", "streamOutput", "project");
        if (!prompt.isEmpty()) {
            return prompt.get(0).get("prompt_text");
        }
        return """
                   请简洁明了回答用户的问题，不知道就回答不知道。
                """;
    }

}
