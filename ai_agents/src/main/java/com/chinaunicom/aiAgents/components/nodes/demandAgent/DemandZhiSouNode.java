package com.chinaunicom.aiAgents.components.nodes.demandAgent;


import com.chinaunicom.aiAgents.entity.ZhisouResp;

import com.chinaunicom.common.flow.context.DemandChatContext;
import com.chinaunicom.common.models.ChatRespType;
import com.chinaunicom.common.sseClient.*;
import com.chinaunicom.common.utils.SseUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.chinaunicom.common.flow.WaitingLock;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import retrofit2.Call;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.fasterxml.jackson.databind.SerializationFeature.INDENT_OUTPUT;

@Slf4j
@LiteflowComponent("demandZhiSouNode")
public class DemandZhiSouNode extends NodeComponent {


    @Resource
    ZhiSouApi zhiSouApi;
    @Resource
    OkHttpClient okHttpClient;

    @Value("${zhisou.path:prod-api}")
    String zhisouPath;




    @Override
    public void process() throws Exception {
        log.info("进入请求智搜结点");

        DemandChatContext chatContext = this.getContextBean(DemandChatContext.class);

        WaitingLock waitingLock = new WaitingLock();
        //Step1 获取智搜token

        //Step2 构造智搜请求体
        Map<String, Object> requestBody = new HashMap<>();
        String query = null;
        if (chatContext.getUserMessage().hasSingleText()) {
            query = chatContext.getUserMessage().singleText();
        }
        Map<String, Object> backGroundInfo = chatContext.getBackGroundInfo();
        backGroundInfo.remove("bpcAuthorization");
        requestBody.put("messageUuid", chatContext.getChatId());
        requestBody.put("sessionUuid", chatContext.getConversationId());
        requestBody.put("ifDeepThink", true);
        requestBody.put("origin", "联通小研");
        requestBody.put("backgroundInfo", backGroundInfo);
        //如果backGroundInfo不为空，把里面的信息拼接到query里面
        try{
            if(backGroundInfo.containsKey("scene")&&!ObjectUtils.isEmpty(backGroundInfo.get("scene"))){
                query = query+"，当前场景："+backGroundInfo.get("scene").toString();
            }
            if(backGroundInfo.containsKey("content")&&!ObjectUtils.isEmpty(backGroundInfo.get("content"))){
                Map<String,String> contentMap = (Map<String,String>)backGroundInfo.get("content");
                if(contentMap.containsKey("名称")&&!ObjectUtils.isEmpty(contentMap.get("名称"))){
                    query = query+"，需求标题："+contentMap.get("名称").toString();
                }
                if(contentMap.containsKey("描述")&&!ObjectUtils.isEmpty(contentMap.get("描述"))){
                    query = query+"，需求描述："+contentMap.get("描述").toString();
                }
                if(contentMap.containsKey("专业线")&&!ObjectUtils.isEmpty(contentMap.get("专业线"))){
                    query = query+"，专业线："+contentMap.get("专业线").toString();
                }
            }

        } catch (Exception e) {
            log.error("解析backGroundInfo字段失败！");
        }
        requestBody.put("query", query);
        log.info("请求智搜参数：{}", requestBody);

        //Step3 建立 SSE 连接
        EventSourceListener eventSourceListener = ThirdSseClientConfig.buildEventSourceListener(new SseHandler() {

            final StringBuilder thinkBuffer = new StringBuilder().append("以下思考和回答来自智搜：\n");
            final StringBuilder totalBuffer = new StringBuilder();
            boolean isThink = false;

            @Override
            public void onNext(String data) {
                ZhisouResp resp = JsonUtil.parseObject(data, ZhisouResp.class);
                /* 智搜独立逻辑 */
                if (resp.getAnswer() != null) {
                    if(!resp.getAnswer().contains("{\"summary_flag\":\"start\"}")
                            &&!resp.getAnswer().contains("{\"summary_flag\":\"end\"}")){
                        totalBuffer.append(resp.getAnswer());
                    }
                    if ("<think>".contentEquals(resp.getAnswer())) {
                        isThink = true;
                    } else if ("</think>".contentEquals(resp.getAnswer())) {
                        isThink = false;
                    } else {
                        if (isThink) {
                            thinkBuffer.append(resp.getAnswer());
                            SseUtils.sendAiMsg(chatContext, thinkBuffer.toString(),"" ,ChatRespType.NORMAL, true);
                        } else {
                            if(!resp.getAnswer().contains("{\"summary_flag\":\"start\"}")
                                    &&!resp.getAnswer().contains("{\"summary_flag\":\"end\"}")){
                                SseUtils.sendAiMsg(chatContext, thinkBuffer.toString(),resp.getAnswer(), ChatRespType.NORMAL, true);
                            }

                        }
                    }
                } else {
                    SseUtils.sendAiMsg(chatContext, thinkBuffer.toString(),"", ChatRespType.NORMAL, true);
                }
            }

            @Override
            public void onComplete() {
                log.info("智搜完整答案 {}", totalBuffer);
                waitingLock.setFlag();
            }

            @Override
            public void onError(Throwable error) {
                log.error("请求智搜异常{}", error.getMessage());

                waitingLock.setFlag();
            }
        });


        ObjectMapper mapper = new ObjectMapper().enable(INDENT_OUTPUT);
        JsonNode jsonNode = mapper.valueToTree(requestBody);

        Call<Map<String, Object>> call = zhiSouApi.zsQuery(zhisouPath,
                jsonNode, "Bearer " + chatContext.getToken());

        EventSources.createFactory(okHttpClient).newEventSource(call.request(), eventSourceListener);
        // 等待流结束
        waitingLock.waitForFlag();
    }

}
