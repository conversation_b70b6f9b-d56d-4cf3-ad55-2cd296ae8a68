package com.chinaunicom.aiAgents.components.nodes.projectAgent;


import com.alibaba.fastjson2.JSONObject;
import com.chinaunicom.aiAgents.components.contexts.Constants;
import com.chinaunicom.aiAgents.dao.mapper.PortalMapper;
import com.chinaunicom.aiAgents.utils.HttpUtils;
import com.chinaunicom.common.configuration.ChatModelInfos;
import com.chinaunicom.common.flow.ProjectQueryInfo;
import com.chinaunicom.common.flow.ThinkingStage;
import com.chinaunicom.common.flow.WaitingLock;
import com.chinaunicom.common.flow.context.InitProjectChatContext;
import com.chinaunicom.common.models.ChatRespSt;
import com.chinaunicom.common.models.ChatRespType;
import com.chinaunicom.common.sseClient.RagApi;
import com.chinaunicom.common.sseClient.SseHandler;
import com.chinaunicom.common.sseClient.ThirdSseClientConfig;
import com.chinaunicom.common.utils.LogUtils;
import com.chinaunicom.common.utils.SseUtils;
import com.chinaunicom.common.utils.StringUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.output.Response;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import retrofit2.Call;

import javax.annotation.Resource;
import java.util.*;

import static com.fasterxml.jackson.databind.SerializationFeature.INDENT_OUTPUT;

/**
 * 背景知识获取节点
 */
@LiteflowComponent("chatBackgroundInfoExtract")
@Slf4j
class ChatBackgroundInfoExtractNode extends NodeComponent {

    @Resource
    RagApi ragApi;
    @Resource
    OkHttpClient okHttpClient;
    @Value("${fileRag.url.prefix:http://10.168.32.85:31416}")
    private String fileRagBaseUrl;


    @Resource
    PortalMapper portalMapper;

    private final ChatLanguageModel chatLanguageModel;

    ChatBackgroundInfoExtractNode(@Autowired ChatModelInfos chatModelInfos) {
        super();
        this.chatLanguageModel = chatModelInfos.getChatLlm(Constants.PROJECT_LLM_NAME, 1.0, false);
    }


    //提取附件中上年项目合作方名称、总投资额
    private String getSystemPrompt() {
        List<Map<String, String>> prompt = portalMapper.queryPrompt("YuanJin", "chatBackgroundInfoExtract", "project");
        if (!prompt.isEmpty()) {
            return prompt.get(0).get("prompt_text");
        }
        return """
                 ##核心任务
                    你的任务是根据用户输入的问题，判定是否需要查询历年历史信息，并返回是或否。
                
                ##任务要求
                    按照以下步骤操作：
                    1. 仔细阅读用户输入的内容。
                    2. 如果问题中包含“上年”“历年”等明确指向历史信息的词汇，则判定为需要查询历年历史信息，返回“是”。
                    3. 如果问题中包含“23年” “2022年” 这种词汇，需要和当前年份做比较，若早于今年，返回“是”。
                    4. 如果问题中不包含上述词汇，则判定为不需要查询历年历史信息，返回“否”。
                    5. 若判定为“是”, 则需要改写原始问题，将原始问题中的“本年” “上年”等字样，替换成25年/24年这样的对应年份。 “今年”“本年”不要替换。
                
                ##输出格式
                    1、如果需要查询历年历史信息，请推理出答案，按如下格式在<json>标签内输出：
                        <json>
                            {
                                "是否需要查询历年": "是",
                                "改写后的问题":  [在此写下“改写后的问题”]
                            }
                        </json>
                    2、如果不需要查询历年历史信息，请直接输出下面<json>标签内的内容：
                        <json>
                            {
                                "是否需要查询历年": "否"
                            }
                        </json>
                """;
    }

    @Override
    public void process() throws Exception {

        InitProjectChatContext chatContext = this.getContextBean(InitProjectChatContext.class);
        if (this.getCurrLoopObj() instanceof ThinkingStage thinkingStage) {

            List<ChatMessage> chatMessages = new ArrayList<>();
            chatMessages.add(SystemMessage.systemMessage(getSystemPrompt()));
            chatMessages.add(UserMessage.from(thinkingStage.getQuestion()));

            try {
                Response<AiMessage> response = chatLanguageModel.generate(chatMessages);
                String ans = response.content().text().replaceAll("<json>", "").replaceAll("</json>", "").trim();
                log.info("ans {}", ans);
//                String ans = """
//                         {
//                        "是否需要查询历年":"是"
//                        "改写后的问题": [在此写下“问题的答案”]
//                          }
//                        """;
                // 使用 Gson 解析 JSON 字符串
                // 获取 "待查询来源" 字段的值
                Map<String, List<String>> sources = new HashMap<>();
                sources.put("页面信息", Collections.singletonList(thinkingStage.getQuestion()));
                sources.put("附件", Collections.singletonList(thinkingStage.getQuestion()));
                JSONObject jsonObject = JSONObject.parseObject(ans);

                if ("是".equals(jsonObject.getString("是否需要查询历年"))) {
                    sources.put("项目历史数据", Collections.singletonList(jsonObject.getString("改写后的问题")));
                }
                thinkingStage.getProjectQueryInfo().setLlmQuery(ans);
                thinkingStage.setSources(sources);
                thinkingStage.getParallelLock().waitForFlag();
                List<SystemMessage> searchSources = getSearchSystemMessages(chatContext, thinkingStage, chatLanguageModel);
                thinkingStage.setSearchSources(searchSources);
            } catch (Exception e) {
                log.error("模型调用异常", e);
                SseUtils.sendAiMsg(chatContext, "模型调用异常，请稍后再试 " + e.getMessage(), ChatRespType.CARD, ChatRespSt.END);
            } finally {
                SseUtils.sendAiMsg(chatContext, "\n", ChatRespType.CARD, ChatRespSt.END);
            }
        } else {
            /* 异常情况处理 */
        }

    }

    public static void main(String[] args) {
        String ans = """
                [
                    {
                        "查询数据来源": "附件",
                        "查询数据信息": ["投资结构", "业务需求", "系统架构", "数据治理", "流程治理", "信创评估", "安全评估", "风险分析"]
                    }
                ]
                """;
        JsonArray jsonArray = JsonParser.parseString(ans).getAsJsonArray();

        // 获取 "待查询来源" 字段的值
        Map<String, List<String>> sources = new HashMap<>();
        jsonArray.asList().forEach(v -> {
            JsonObject jsonObject = v.getAsJsonObject();
            sources.put(jsonObject.get("查询数据来源").getAsString(), jsonObject.get("查询数据信息").getAsJsonArray().asList().stream().map(JsonElement::getAsString).toList());
        });
        System.out.println(sources);
    }


    private List<SystemMessage> getSearchSystemMessages(InitProjectChatContext chatContext, ThinkingStage thinkingStage, ChatLanguageModel chatLanguageModel) {
        ProjectQueryInfo queryInfo = thinkingStage.getProjectQueryInfo();
        Map<String, String> bgMap = new HashMap<>();
        List<SystemMessage> systemMessages = new ArrayList<>();
        Map<String, List<String>> sourceMaps = thinkingStage.getSources();
        for (String k : sourceMaps.keySet()) {
            //组装页面背景知识
            if (sourceMaps.containsKey("页面信息")) {
                String pageInfo = getPageInfoText(JSONObject.toJSONString(thinkingStage.getProjectQueryInfo().getPageInfo()), thinkingStage.getQuestion(), chatLanguageModel);
//               pageInfo = JSONObject.toJSONString(thinkingStage.getProjectueryInfo().getPageInfo());
                if (StringUtils.isNotEmpty(pageInfo)) {
                    pageInfo = pageInfo.length() > 8000 ? pageInfo.substring(0, 8000) : pageInfo;
                    systemMessages.add(SystemMessage.systemMessage("页面信息：" + pageInfo));
                    Map<String, String> cardMap = new HashMap<>();
                    cardMap.put("title", "页面信息：" + thinkingStage.getQuestion());
                    cardMap.put("content", (pageInfo.length() > 100 ? pageInfo.substring(0, 100) : pageInfo));
                    SseUtils.sendAiMsg(chatContext, JSONObject.toJSONString(cardMap), ChatRespType.CARD, ChatRespSt.START);
                    bgMap.put("页面信息", pageInfo);
                }
            }
            //组装附件背景知识
            if (sourceMaps.containsKey("附件")) {
                queryInfo.setUserId(chatContext.getUserId());
                queryInfo.setChatId(chatContext.getChatId());
                List<String> fileQuerys = sourceMaps.get("附件");
                int i = 0;
                for (String fileQuery : fileQuerys) {
                    i++;
                    String fileText = getContentFromRagApi(queryInfo, fileQuery);
                    if (StringUtils.isNotEmpty(fileText)) {
                        fileText = fileText.length() > 2000 ? fileText.substring(0, 2000) : fileText;
                        Map<String, String> cardMap = new HashMap<>();
                        cardMap.put("title", "附件信息：" + fileQuery);
                        cardMap.put("content", (fileText.length() > 100 ? fileText.substring(0, 100) : fileText));
                        systemMessages.add(SystemMessage.systemMessage("附件内容：" + fileText));
                        SseUtils.sendAiMsg(chatContext, JSONObject.toJSONString(cardMap), ChatRespType.CARD, ChatRespSt.START);
                        bgMap.put("附件信息" + i, fileText);
                    }
                }
            }

            //组装项目历史知识
            if (sourceMaps.containsKey("项目历史数据")) {
                List<String> historyQuerys = sourceMaps.get("项目历史数据");
                String text = getContentFromText2Sql(queryInfo, String.join(",", historyQuerys));
                if (StringUtils.isNotEmpty(text)) {
                    text = text.length() > 8000 ? text.substring(0, 8000) : text;
                    Map<String, String> cardMap = new HashMap<>();
                    cardMap.put("title", "项目历史数据：" + String.join(",", historyQuerys));
                    cardMap.put("content", (text.length() > 100 ? text.substring(0, 100) : text));
                    systemMessages.add(SystemMessage.systemMessage("项目历史数据：" + text));
                    SseUtils.sendAiMsg(chatContext, JSONObject.toJSONString(cardMap), ChatRespType.CARD, ChatRespSt.START);
                    bgMap.put("项目历史数据", text);
                }
            }

        }
        return systemMessages;
    }


    /**
     * 调用大模型提炼页面知识
     * }
     */
    public String getPageInfoText(String pageInfo, String pageQuery, ChatLanguageModel chatLanguageModel) {
        log.info("pageInfo {},pageQuery {}", pageInfo, pageQuery);
        String prompt =
                """ 
                        你的任务是用以上页面信息内容作为背景知识，根据用户提出的问题，提取出相关的信息，并组装成文本内容输出。
                        这是用户提出的问题：
                        """;
        try {
            List<ChatMessage> chatMessages = new ArrayList<>();
            chatMessages.add(SystemMessage.systemMessage(pageInfo));
            chatMessages.add(SystemMessage.systemMessage(prompt));
            chatMessages.add(UserMessage.from(pageQuery));

            Response<AiMessage> response = chatLanguageModel.generate(chatMessages);
            String text = response.content().text();
            log.info("调用 getPageInfoText 接口，响应内容:{}", text);
            return text;
        } catch (Exception e) {
            log.error("调用 getPageInfoText 接口，错误信息:{}", e);
            return null;
        }
    }

    /**
     * 调用谢兆伟的text2sql接口获取数据
     * 测试环境：http://10.168.32.90:31258/text2sql/external/api
     * 生产环境：http://10.168.32.85:31920/text2sql/external/api
     * POST请求：
     * application/json
     * <p>
     * {"request_id":"12321232",  --请求id  {request.user_id}_{request.chat_id}_{random.randint(1000, 9999)}
     * "router":"project_data",   --固定值
     * "query":"数字化研发平台在2024年自研投资占比有没有超过50%"  --用户问题
     * }
     */
    public String getContentFromText2Sql(ProjectQueryInfo queryInfo, String query) {
        log.info("queryInfo {},query {}", queryInfo, query);
        StringBuffer sb = new StringBuffer();
        Map<String, String> params = new HashMap();
        params.put("user_id", queryInfo.getUserId());
        params.put("chat_id", queryInfo.getChatId());
        params.put("query", query + " 当前项目名称：" + queryInfo.getProjectName());
        InitProjectChatContext chatContext = this.getContextBean(InitProjectChatContext.class);
        WaitingLock waitingLock = new WaitingLock();
        EventSourceListener eventSourceListener = ThirdSseClientConfig.buildEventSourceListener(new SseHandler() {
            String think = "";

            @Override
            public void onNext(String token) {
                try {
                    log.info("{}", token);
                    if (StringUtils.isJsonString(token)) {
                        Map map = JSONObject.parseObject(token, Map.class);
                        String content = MapUtils.getString(map, "content");
                        think = MapUtils.getString(map, "think").replace(think, "");
                        SseUtils.sendAiMsg(chatContext, think, ChatRespType.CARD, ChatRespSt.START);
                        sb.append(content);
                    }
                } catch (Exception e) {
                    log.error("", e);
                }
            }

            @Override
            public void onComplete() {
                waitingLock.setFlag();
            }

            @Override
            public void onError(Throwable error) {
                waitingLock.setFlag();
                log.error(error.getMessage());
            }
        });

        try {
            ObjectMapper OBJECT_MAPPER = new ObjectMapper().enable(INDENT_OUTPUT);
            JsonNode jsonNode = OBJECT_MAPPER.readTree(JSONObject.toJSONString(params));
            Call<Map<String, Object>> call = ragApi.text2Sql(jsonNode);
            EventSources.createFactory(okHttpClient).newEventSource(call.request(), eventSourceListener);
            waitingLock.waitForFlag();
            log.info("调用 getContentFromText2Sql 接口，响应内容:{}", sb);
            return sb.toString();
        } catch (Exception e) {
            log.error("调用 getContentFromText2Sql 接口，错误信息:{}", e);
            return null;
        }

    }

    /**
     * 调用莫宗军的Rag接口获取数据
     * 测试环境：http://10.168.32.85:31416/retrieve_file/search
     * POST请求：
     * application/json
     * 请求入参：
     * {
     * "file_id": "test1234",
     * "question": "你好"
     * return：{"code":200,"message":"success","data":[]}
     * }
     */
    public String getContentFromRagApi(ProjectQueryInfo queryInfo, String fileQuery) {
        log.info("queryInfo {},fileQuery {}", queryInfo, fileQuery);
        StringBuilder sb = new StringBuilder();
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("file_ids", queryInfo.getFileKeys());
            params.put("source", queryInfo.getMode());
            params.put("knowledge_id", queryInfo.getFunc());
            params.put("question", fileQuery);
            String uri = fileRagBaseUrl + "/retrieve_file/search";
            Map<String, Object> responseMap = HttpUtils.httpPost(params, uri);
            if (MapUtils.getInteger(responseMap, "code") == 200) {

                List<Map> data = (List<Map>) responseMap.get("data");
                if (!data.isEmpty() && data.size() > 0) {
                    for (Map page : data) {
                        sb.append("附件【").append(MapUtils.getString(page, "file_name")).append("】第" + MapUtils.getInteger(page, "page") + "页，内容：")
                                .append(MapUtils.getString(page, "text"));
                    }
                }
                log.info("调用 getContentFromRagApi 接口，响应内容:{}", sb);
                return sb.toString();
            }
        } catch (Exception e) {
            log.error("调用 getContentFromRagApi 接口，错误信息:{}", e);
            return sb.toString();
        }
        return null;
    }
}
