package com.chinaunicom.aiAgents.service;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.chinaunicom.aiAgents.entity.BpcUser;
import com.chinaunicom.aiAgents.entity.PendingItem;
import com.chinaunicom.aiAgents.entity.ResponseModel;
import com.chinaunicom.aiAgents.utils.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * HTTP客户端服务
 * 对应Python中的HTTP请求处理逻辑
 */
@Service
@Slf4j
public class HttpClientService {


    @Value("${tianti.base.url:https://devops.chinaunicom.cn}")
    private String taintiBaseUrl;


    //@Value("${dm.zhi.sou.token.url:https://10.124.151.16:19530/dev-api/sso/developPlatformSsoGetToken}")
//    @Value("${dm.zhi.sou.token.url:https://10.238.57.69:80/prod-api/sso/developPlatformSsoGetToken}")
//    private String zhiSouTokenUrl;

    @Value("${zhisou.url:https://10.238.57.69:80/}")
    String zhisouUrl;

    @Value("${zhisou.path:prod-api}")
    String zhisouPath;


    private final RestTemplate restTemplate;

    private static TrustManager[] createTrustAllCerts() {
        return new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public void checkServerTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[]{};
                    }
                }
        };
    }

    public HttpClientService() {
        try {


            Duration timeout = Duration.ofSeconds(10000);
            OkHttpClient.Builder okHttpClientBuilder = new OkHttpClient.Builder()
                    .callTimeout(timeout)
                    .connectTimeout(timeout)
                    .readTimeout(timeout)
                    .writeTimeout(timeout);

            // 创建信任所有证书的 TrustManager
            TrustManager[] trustAllCerts = createTrustAllCerts();
            // 获取 SSLContext 实例
            SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
            // 获取 SSLSocketFactory 实例
            SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

            // 配置 OkHttpClient 跳过证书校验
            okHttpClientBuilder.sslSocketFactory(sslSocketFactory, (X509TrustManager) trustAllCerts[0]);
            okHttpClientBuilder.hostnameVerifier((hostname, session) -> true);

            OkHttp3ClientHttpRequestFactory requestFactory = new OkHttp3ClientHttpRequestFactory(okHttpClientBuilder.build());


            // 使用自定义的 requestFactory 创建 RestTemplate
            this.restTemplate = new RestTemplate(requestFactory);
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            log.error("初始化 RestTemplate 时配置 SSL 失败", e);
            throw new RuntimeException("初始化 RestTemplate 时配置 SSL 失败", e);
        }
    }

    /**
     * 发送待办到ai_sa接口
     * 对应Python中的send_to_ai_sa函数
     */
    public ResponseModel sendToAiSa(PendingItem pendingItem) {
        String url = taintiBaseUrl + "/ai_sa/pending/addPendingReview";

        log.info("发送待办到ai_sa接口: {}", url);

        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Accept", "application/json");

            // 创建请求实体
            HttpEntity<PendingItem> requestEntity = new HttpEntity<>(pendingItem, headers);

            // 发送POST请求
            ResponseEntity<ResponseModel> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    ResponseModel.class
            );

            if (response.getStatusCode() == HttpStatus.OK) {
                log.info("ai_sa接口调用成功");
                return response.getBody();
            } else {
                log.error("ai_sa接口返回错误状态码: {}", response.getStatusCode());
                throw new RuntimeException("外部服务返回错误: " + response.getStatusCode());
            }
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("调用ai_sa接口失败，HTTP错误: {}, 响应: {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("外部服务返回错误: " + e.getStatusCode() + ", 响应: " + e.getResponseBodyAsString(), e);
        } catch (ResourceAccessException e) {
            log.error("调用ai_sa接口失败，网络错误: {}", e.getMessage(), e);
            throw new RuntimeException("调用外部服务失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("调用ai_sa接口失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用外部服务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新小研待办状态
     * 对应Python中updateXiaoYanTaskStatus函数中的外部API调用
     */
    public boolean updatePendingReview(String pendingId, String userEmail, int stateLogic, String stateShow) {
        String url = taintiBaseUrl + "/ai_sa/pending/updatePendingReview";

        log.info("更新小研待办状态: pendingId={}, userEmail={}, stateLogic={}, stateShow={}",
                pendingId, userEmail, stateLogic, stateShow);

        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Accept", "application/json");

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("pendingId", pendingId);
            requestBody.put("userEmail", userEmail);
            requestBody.put("stateLogic", stateLogic);
            requestBody.put("stateShow", stateShow);

            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            if (response.getStatusCode() == HttpStatus.OK) {
                log.info("更新小研待办状态成功");
                return true;
            } else {
                log.error("更新小研待办状态失败，状态码: {}", response.getStatusCode());
                throw new RuntimeException("外部服务返回错误: " + response.getStatusCode());
            }
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("更新小研待办状态失败，HTTP错误: {}, 响应: {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("外部服务返回错误: " + e.getStatusCode() + ", 响应: " + e.getResponseBodyAsString(), e);
        } catch (ResourceAccessException e) {
            log.error("更新小研待办状态失败，网络错误: {}", e.getMessage(), e);
            throw new RuntimeException("调用外部服务失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("更新小研待办状态失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用外部服务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取当前登录人信息
     */
    public BpcUser getBpcCurrentUserInfo(String bpcAuthorization) {
        String url = taintiBaseUrl + "/bit-bpc-process/user/currentUser";
        log.info("获取当前登录人信息: bpcAuthorization={}", bpcAuthorization);
        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.set("Authorization", bpcAuthorization);

            // 创建请求实体
            HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

            // 发送Get请求1
            ResponseEntity<ReturnResult> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    requestEntity,
                    ReturnResult.class
            );

            log.info("获取当前登录人信息响应状态码: {}", response.getStatusCode());
            if (response.getStatusCode() == HttpStatus.OK) {

                log.info("获取当前登录人信息响应头: {}", response.getHeaders());
                log.info("获取当前登录人信息响应内容:{}", response.getBody());
                Object data = Objects.requireNonNull(response.getBody()).getData();
                String jsonData = JSON.toJSONString(data);
                return JSON.parseObject(jsonData, BpcUser.class);//
            } else {
                log.error("获取当前登录人信息失败，状态码: {}", response.getStatusCode());
                throw new RuntimeException("外部服务返回错误: " + response.getStatusCode());
            }
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("获取当前登录人信息失败，HTTP错误: {}, 响应: {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("外部服务返回错误: " + e.getStatusCode() + ", 响应: " + e.getResponseBodyAsString(), e);
        } catch (ResourceAccessException e) {
            log.error("获取当前登录人信息失败，网络错误: {}", e.getMessage(), e);
            throw new RuntimeException("调用外部服务失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("获取当前登录人信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用外部服务失败: " + e.getMessage(), e);
        }
    }


    /**
     * 获取智搜token
     */
    public String getZhiSouToken(Map<String, String> map) {
        String url = zhisouUrl + zhisouPath + "/sso/developPlatformSsoGetToken";
        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            // 构建表单数据（注意这里是 MultiValueMap）
            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            for (Map.Entry<String, String> entry : map.entrySet()) {
                formData.add(entry.getKey(), entry.getValue());
            }
            // 创建请求实体
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(formData, headers);

            // 发送Post请求
            ResponseEntity<JSONObject> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    JSONObject.class
            );

            log.info("智搜token状态码: {}", response.getStatusCode());
            if (response.getStatusCode() == HttpStatus.OK) {
                log.info("智搜token响应头: {}", response.getHeaders());
                log.info("智搜token响应内容: {}", response.getBody());
                return Objects.requireNonNull(response.getBody()).getJSONObject("data").getString("token");
            } else {
                log.error("获取智搜token失败，状态码: {}", response.getStatusCode());
                throw new RuntimeException("外部服务返回错误: " + response.getStatusCode());
            }
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("获取智搜token失败，HTTP错误: {}, 响应: {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("外部服务返回错误: " + e.getStatusCode() + ", 响应: " + e.getResponseBodyAsString(), e);
        } catch (ResourceAccessException e) {
            log.error("获取智搜token失败，网络错误: {}", e.getMessage(), e);
            throw new RuntimeException("调用外部服务失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("获取智搜token失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用外部服务失败: " + e.getMessage(), e);
        }
    }
    /**
     * 获取智搜Redirect
     */
    public String getZhiSouRedirect(Map<String, String> map) {
        String url = zhisouUrl + zhisouPath + "/sso/developPlatformSsoRedirect";
        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            // 构建表单数据（注意这里是 MultiValueMap）
            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            for (Map.Entry<String, String> entry : map.entrySet()) {
                formData.add(entry.getKey(),entry.getValue());
            }
            // 创建请求实体
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(formData, headers);

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            log.info("智搜Redirect状态码: {}", response.getStatusCode());
            if (response.getStatusCode() == HttpStatus.OK) {
                log.info("获取智搜Redirect成功");
                log.info("智搜Redirect响应头: {}", response.getHeaders());
                log.info("智搜Redirect响应内容: {}", response.getBody());
                return response.getBody();
            } else {
                log.error("获取智搜Redirect失败，状态码: {}", response.getStatusCode());
                throw new RuntimeException("外部服务返回错误: " + response.getStatusCode());
            }
        }catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("获取智搜Redirect失败，HTTP错误: {}, 响应: {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("外部服务返回错误: " + e.getStatusCode() + ", 响应: " + e.getResponseBodyAsString(), e);
        } catch (ResourceAccessException e) {
            log.error("获取智搜Redirect失败，网络错误: {}", e.getMessage(), e);
            throw new RuntimeException("调用外部服务失败: " + e.getMessage(), e);
        }catch (Exception e) {
            log.error("获取智搜Redirect失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用外部服务失败: " + e.getMessage(), e);
        }
    }


    public String getZhiSouTokenByOneStep(String bpcAuthorization) {
        BpcUser userInfo = getBpcCurrentUserInfo(bpcAuthorization);
        Map<String, String> authData = new HashMap<>();
        authData.put("loginName", userInfo.getUserAccount());
        authData.put("userName", userInfo.getUserName());
        authData.put("email", userInfo.getEmail());
        authData.put("phoneNumber", userInfo.getMobile());
        authData.put("organizationCode", userInfo.getDeptID());
        authData.put("organizationName", userInfo.getDeptName());

        log.info("zhisou 接口请求参数auth_data:{}", authData);
        return getZhiSouToken(authData);
    }

}