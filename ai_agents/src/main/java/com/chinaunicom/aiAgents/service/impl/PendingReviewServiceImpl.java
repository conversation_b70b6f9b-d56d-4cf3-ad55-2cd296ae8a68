package com.chinaunicom.aiAgents.service.impl;


import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.chinaunicom.aiAgents.dao.domain.*;
import com.chinaunicom.aiAgents.dao.mapper.AiAgentConfigMapper;
import com.chinaunicom.aiAgents.dao.mapper.AiPendingReviewMapper;
import com.chinaunicom.aiAgents.dao.mapper.AiTalkMapper;
import com.chinaunicom.aiAgents.dao.mapper.UserInfoMapper;
import com.chinaunicom.aiAgents.entity.ResultMessage;
import com.chinaunicom.aiAgents.service.demandAssistant.PendingReviewService;

import com.chinaunicom.common.utils.StringUtils;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

@DS("portal")
@Service
public class PendingReviewServiceImpl implements PendingReviewService {
    @Value("${project.assistant.url}")
    private String assistantUrl;

    @Resource
    AiAgentConfigMapper agentConfigMapper;
    @Resource
    AiPendingReviewMapper pendingReviewMapper;
    @Resource
    AiTalkMapper talkMapper;
    @Resource
    UserInfoMapper userInfoMapper;


    private static AiAgentConfig XQAgent;
    private static AiAgentConfig XMZAgent;


    /**--- 内部工具方法 ----**/
    //自动创建对话
    private void autoCreateTalk(List<String> userList, String agentId, String agentName){
        for (String userId: userList){
            List<String> tmpList = talkMapper.queryTalkIdsByMembers(userId, agentId, AiTalk.TALK_TYPE_SINGLE_AGENT);
            if(tmpList.isEmpty()){
                //新建一个talk 注意 还是以userId 作为talk的创建人
                //2. 核心创建talk 本身
                // 单人talk 名称由 好友名决定  这里时agentId对应的name
                String talkName =  agentName;
                String talkId = StringUtils.getUUID();
                Date createTime = new Date();
                String talkType = AiTalk.TALK_TYPE_SINGLE_AGENT;
                talkMapper.createSingleTalk(talkId, talkName, userId , createTime, talkType);

                //3. 增加个人对talk的 个人设置
                List<TalkPersonalSetting> pSettings = new ArrayList<>();
                pSettings.add( new TalkPersonalSetting(userId, talkId, talkName) );
                talkMapper.multiAddPersonalSetting(pSettings);

                //单人聊天也和群聊一样，  涉及的人员存入 talk_members表中
                List<TalkMember> talkMembers = new ArrayList<>();
                talkMembers.add( new TalkMember(userId, "default", TalkMember.TALK_MEMBER_TYPE_USER) );
                talkMembers.add( new TalkMember(agentId, agentName, TalkMember.TALK_MEMBER_TYPE_AGENT) );
                //实际新建时  talkMembers 中的memberName没有意义   上面userName给 "default" 不影响
                talkMapper.addMembersToTalk(talkId, talkMembers, createTime);
            }
        }
    }

    //更新待办
    private ResultMessage updatePending(String userEmail, String pendingId, String stateShow, int stateLogic, String countersign){
        ResultMessage resultMessage = new ResultMessage();
        resultMessage.success();

        //需要手动查一下 用户id
        List<Map> tmpIdEmailList = userInfoMapper.queryUserByEmails(Arrays.asList(userEmail));
        String responsibleUser = (String) tmpIdEmailList.get(0).get("id");

        //保护操作 先查 若无数据直接报错
        Map upMap = pendingReviewMapper.queryUserPending(responsibleUser, pendingId);
        if(upMap == null){
            resultMessage.fail();
            resultMessage.setMessage("待办不存在!");
        }else{

            //更新主表 ai_pending_review
            // todo 后续考虑一对多的场景若该待办的所有 负责人的状态都是完成了

            // 或签or：任一人员处理，则待办完成；会签and：所有人员都处理，则待办完成
            if(countersign != null && countersign.equals("or")){
                // 更新所有人的代办人员表
                pendingReviewMapper.updatePendingAllUserState(pendingId, stateShow, stateLogic);
                // 更新待办表
                pendingReviewMapper.updatePendingState(pendingId,stateShow,stateLogic);
            }else {
                //更新操作人的代办人员表
                pendingReviewMapper.updateUserPendingState(pendingId,responsibleUser, stateShow, stateLogic);
                // 待办所有负责人都处理了
                int i = pendingReviewMapper.queryPendingUserCount(pendingId);
                if(i == 0){
                    // 更新待办表
                    pendingReviewMapper.updatePendingState(pendingId,stateShow,stateLogic);
                }
            }

            //同步更新message
            String msgId = MapUtils.getString(upMap,"msg_id");
            if(msgId != null){
                Map orgMessage = talkMapper.findMessageById(msgId);
                //保护一下  防止message已经被删了
                if(orgMessage != null){
                    //待办 类型的message  数据结构一定是  {"pendingContent":{"state_show":"xxxx"}}
                    String messageStr = MapUtils.getString(orgMessage,"message");
                    Map jsonObj = JSONObject.parseObject(messageStr);
                    //只替换 state_show 其余字段不变
                    ((Map) jsonObj.get("pendingContent")).put("state_show",stateShow);
                    String newMsgStr = JSONObject.toJSONString(jsonObj);

                    talkMapper.updateMessageContent(msgId, newMsgStr);
                }
            }

        }
        return resultMessage;
    }


    //    @Transactional(rollbackFor = Exception.class)  !!!反面教材 千万注意!!! 用下面那个事务
    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public ResultMessage xqCreatePending(Map params) throws Exception {
        ResultMessage resultMessage = new ResultMessage();
        resultMessage.success();

        //获取需求助手的 agent  todo 每次都差浪费了些
        if(XQAgent == null){
            AiAgentConfig agent = agentConfigMapper.selectByAgentName(AiPendingReview.BELONG_MODULE_XQ);
            if( agent == null){
                //归属模块有问题 直接返回报错
                resultMessage.fail();
                resultMessage.setData("未找到 belongModule:" + AiPendingReview.BELONG_MODULE_XQ + " 对应智能体!");
                return  resultMessage;
            }
            XQAgent = agent;
        }

        //todo 负责人需要 按照小研的用户体系做个过滤。
        //待办的负责人 都是email
        List<String> responseEmails = (List) params.get("responsibleList");
        //待办创建人
        String creatorEmail = MapUtils.getString(params, "creatorEmail");

        //由于需求那边 用户id login 与门户信息不一致，只能有userId  需要手动查user表
        List<String> emails = new ArrayList<>(responseEmails);
        emails.add(creatorEmail);
        List<Map> tmpIdEmailList = userInfoMapper.queryUserByEmails(emails);
        String creator = null;
        List<String> responseList = new ArrayList<>();
        for (Map map : tmpIdEmailList) {
            String tmpId = MapUtils.getString(map,"id");
            String email = MapUtils.getString(map,"email");
            if(creatorEmail.equals(email)){
                creator = tmpId;
            }
            if(responseEmails.contains(email)){
                responseList.add(tmpId);
            }
        }

        //待办的标题
        String title = MapUtils.getString(params,"title");
        //待办的查看详情 操作按钮url地址
        String url = MapUtils.getString(params,"url");
        //主需求 所处的流程 id
        String processInstanceId = MapUtils.getString(params,"processInstanceId");
        //流程名称
        String processName = MapUtils.getString(params,"processName");
        //系统需求编号
        String dmNumber = MapUtils.getString(params, "dmNumber");
        //流程的阶段
        String taskName = MapUtils.getString(params,"taskName");
        //todo 需要原系统传递 type类型  是待办还是待阅
        String type = MapUtils.getString(params,"type");
        type = type == null ? AiPendingReview.PENDING_REVIEW_TYPE_TODO : type;
        //todo 需要原系统传递  countersign 是会签还是或签
        String countersign = MapUtils.getString(params,"countersign");
        countersign = countersign == null ? AiPendingReview.PENDING_COUNTER_SIGN_AND : countersign;
        //下游若显示传递状态名称则取用 否则默认都是新建
        String stateShow = MapUtils.getString(params, "stateShow");
        stateShow = stateShow == null ? AiPendingReview.PENDING_DEFAULT_STATE_SHOW : stateShow;

        //todo 后续直接操作类待办 buttons数据  暂未使用
//        List<Map> buttonsJson = (List) params.get("buttonsJson");

        //上下问题背景信息 是最终给大模型拼接提示词用的
        // value都是字符串。  key可以是中文 可以英文
        Map backgroundInfo = (Map) params.get("backgroundInfo");
        backgroundInfo = backgroundInfo == null? new HashMap():backgroundInfo;

        //todo ai_summary的逻辑生成部分 先简单写下
        //todo 类似的 title 可能也需要二次加工  先不管
        String aiSummary = """
                你有一个待办【%s】%s
                """.formatted(dmNumber,title);

        String agentId = XQAgent.getAgentId();
        String agentName = XQAgent.getName();

        /** 原aisa 部分逻辑  待办的实际落表床i教案 **/
        AiPendingReview pendingReviewItem = new AiPendingReview();
        //待办的id
        String id = StringUtils.getUUID();

        pendingReviewItem.setId(id);
        pendingReviewItem.setPendId(dmNumber);
        pendingReviewItem.setPendModel(AiPendingReview.MODULE_XQ);
        pendingReviewItem.setPendFun(processName);
        pendingReviewItem.setPendAct(taskName);
        pendingReviewItem.setTitle(title);
        pendingReviewItem.setType(type);
        pendingReviewItem.setCountersign(countersign);
        pendingReviewItem.setUrl(url);
        pendingReviewItem.setBelongModule(AiPendingReview.BELONG_MODULE_XQ);
        pendingReviewItem.setAgentId(agentId);
        pendingReviewItem.setAiSummary(aiSummary);
        pendingReviewItem.setBackgroundInfo(JSONObject.toJSONString(backgroundInfo));
        pendingReviewItem.setCreator(creator);
        pendingReviewItem.setStateShow(stateShow);

        //根据type 确定初始状态
        if (AiPendingReview.PENDING_REVIEW_TYPE_TODO.equals(type)) {
            pendingReviewItem.setStateLogic(AiPendingReview.PENDING_REVIEW_STATE0);
        }else{
            pendingReviewItem.setStateLogic(AiPendingReview.PENDING_REVIEW_STATE3);
        }

        //todo 整体加事务回滚
        pendingReviewMapper.addPending(pendingReviewItem);
        pendingReviewMapper.addPendingResponsibleUser(
                pendingReviewItem.getId(),
                responseList,
                pendingReviewItem.getStateShow(),
                pendingReviewItem.getStateLogic(),
                null
        );

        //对responseList 负责人最多5~6个 遍历处理
        // 校验 这个负责人和 agent是否有对话
        // 若无 则帮这个人新建一个 与该agent的talk
        autoCreateTalk(responseList,agentId,agentName);

        //接口关键信息返回
        Map resMap = new HashMap();
        resMap.put("pendingId",id);
        resMap.put("title", title);
        resMap.put("type", type);
        resultMessage.setData(resMap);
        return resultMessage;
    }

    @Override
    public ResultMessage xmzCreatePending(Map params) throws Exception {
        ResultMessage resultMessage = new ResultMessage();
        resultMessage.success();

        //获取需求助手的 agent  todo 每次都差浪费了些
        if(XMZAgent == null){
            AiAgentConfig agent =  agentConfigMapper.selectByAgentName(AiPendingReview.BELONG_MODULE_XMZ);
            if( agent == null){
                //归属模块有问题 直接返回报错
                resultMessage.fail();
                resultMessage.setData( "未找到 belongModule:"+AiPendingReview.BELONG_MODULE_XMZ+ " 对应智能体!");
                return  resultMessage;
            }
            XMZAgent = agent;
        }

        //待办的负责人 都是email
        List<String> responseEmails = (List) params.get("responsibleList");
        //待办创建人
        String creatorEmail = MapUtils.getString(params, "creatorEmail");

        //由于需求那边 用户id login 与门户信息不一致，只能有userId  需要手动查user表
        List<String> emails = new ArrayList<>(responseEmails);
        emails.add(creatorEmail);
        List<Map> tmpIdEmailList = userInfoMapper.queryUserByEmails(emails);
        String creator = null;
        List<String> responseList = new ArrayList<>();
        for (Map map : tmpIdEmailList) {
            String tmpId = MapUtils.getString(map,"id");
            String email = MapUtils.getString(map,"email");
            if(creatorEmail.equals(email)){
                creator = tmpId;
            }
            if(responseEmails.contains(email)){
                responseList.add(tmpId);
            }
        }

        //待办的标题
        String title = MapUtils.getString(params,"title");
        //待办的查看详情 操作按钮url地址
        String url = MapUtils.getString(params,"url");

        String urlEncode = null;
        try {
            urlEncode = URLEncoder.encode(url, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            //url编码有问题 直接返回报错
            resultMessage.fail();
            resultMessage.setData( "url编码失败:"+url);
            return  resultMessage;
        }
        url = assistantUrl + urlEncode;

        //需要原系统传递 type类型  是待办还是待阅
        String type = MapUtils.getString(params,"type");
        type = type == null? AiPendingReview.PENDING_REVIEW_TYPE_TODO:type;
        //需要原系统传递  countersign 是会签还是或签
        String countersign = MapUtils.getString(params,"countersign");
        countersign = countersign == null? AiPendingReview.PENDING_COUNTER_SIGN_AND :countersign;
        //下游若显示传递状态名称则取用 否则默认都是新建
        String stateShow = MapUtils.getString(params, "stateShow");
        stateShow = stateShow == null? AiPendingReview.PENDING_DEFAULT_STATE_SHOW : stateShow;

        //实例id 所属模块 所属流程 所属操作
        String pendId = MapUtils.getString(params,"id");
        String pendFun = MapUtils.getString(params,"fun");
        String pendModel = MapUtils.getString(params,"model");
        String pendAct = MapUtils.getString(params,"act");

        //项目制 ai_summary下游直接生成的 固定文字  后续可以修改
        String aiSummary = MapUtils.getString(params,"aiSummary");

        //todo 后续直接操作类待办 buttons数据  暂未使用
//        List<Map> buttonsJson = (List) params.get("buttonsJson");

        //上下问题背景信息 是最终给大模型拼接提示词用的
        // value都是字符串。  key可以是中文 可以英文
        Map backgroundInfo = (Map) params.get("backgroundInfo");
        backgroundInfo = backgroundInfo == null? new HashMap():backgroundInfo;


        String agentId = XMZAgent.getAgentId();
        String agentName = XMZAgent.getName();

        /** 原aisa 部分逻辑  待办的实际落表床i教案 **/
        AiPendingReview pendingReviewItem = new AiPendingReview();
        //待办的id
        String id = StringUtils.getUUID();

        pendingReviewItem.setId(id);
        pendingReviewItem.setPendId(pendId);
        pendingReviewItem.setPendModel(AiPendingReview.MODULE_XMZ);
        pendingReviewItem.setPendFun(pendFun);
        pendingReviewItem.setPendAct(pendAct);
        pendingReviewItem.setTitle(title);
        pendingReviewItem.setType(type);
        pendingReviewItem.setCountersign(countersign);
        pendingReviewItem.setUrl(url);
        pendingReviewItem.setBelongModule(AiPendingReview.BELONG_MODULE_XMZ);
        pendingReviewItem.setAgentId(agentId);
        pendingReviewItem.setAiSummary(aiSummary);
        pendingReviewItem.setBackgroundInfo(JSONObject.toJSONString(backgroundInfo));
        pendingReviewItem.setCreator(creator);
        pendingReviewItem.setStateShow(stateShow);

        //根据type 确定初始状态
        if(AiPendingReview.PENDING_REVIEW_TYPE_TODO.equals(type)){
            pendingReviewItem.setStateLogic(AiPendingReview.PENDING_REVIEW_STATE0 );
        }else{
            pendingReviewItem.setStateLogic(AiPendingReview.PENDING_REVIEW_STATE3);
        }

        //todo 整体加事务回滚
        pendingReviewMapper.addPending(pendingReviewItem);
        pendingReviewMapper.addPendingResponsibleUser(
                pendingReviewItem.getId(),
                responseList,
                pendingReviewItem.getStateShow(),
                pendingReviewItem.getStateLogic(),
                null
        );

        //对responseList 负责人最多5~6个 遍历处理
        // 校验 这个负责人和 agent是否有对话
        // 若无 则帮这个人新建一个 与该agent的talk
        autoCreateTalk(responseList,agentId,agentName);

        //接口关键信息返回
        Map resMap = new HashMap();
        resMap.put("pendingId",id);
        resMap.put("title", title);
        resMap.put("type", type);
        resultMessage.setData(resMap);
        return resultMessage;

    }

    @Override
    public ResultMessage xqUpdatePending(Map params) throws Exception {
        //下游参数   哪条待办  哪个人操作的  (操作人  一定要是这条待办的负责人)
        // 需求代办 代办ID
        String pendingId = MapUtils.getString(params,"pendingId");
        AiPendingReview item = pendingReviewMapper.queryPendingById(pendingId);
        String countersign = item.getCountersign();

        String userEmail = MapUtils.getString(params,"userEmail");
        String stateShow = MapUtils.getString(params,"stateShow");
        Integer stateLogic = MapUtils.getInteger(params,"stateLogic");

        return updatePending(userEmail,pendingId,stateShow,stateLogic,countersign);
    }

    @Override
    public ResultMessage xmzUpdatePending(Map params) throws Exception {
        // 项目制代办 查询
        String pendId = MapUtils.getString(params,"id");
        String pendingFun = MapUtils.getString(params,"fun");
        String pendingModel = MapUtils.getString(params,"model");
        AiPendingReview item = pendingReviewMapper.queryItemByPendIdAndPendFunAndPendModel(pendId, pendingFun, pendingModel);
        String pendingId = item.getId();
        String countersign = item.getCountersign();

        String userEmail = MapUtils.getString(params,"userEmail");
        String stateShow = MapUtils.getString(params,"stateShow");
        Integer stateLogic = MapUtils.getInteger(params,"stateLogic");

        return updatePending(userEmail,pendingId,stateShow,stateLogic,countersign);
    }
}
