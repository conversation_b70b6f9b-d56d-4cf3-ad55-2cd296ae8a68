package com.chinaunicom.aiAgents.service.demandAssistant;


import com.chinaunicom.aiAgents.dao.domain.DmSearchVector;
import com.chinaunicom.aiAgents.dao.mapper.DmInfoMapper;
import com.chinaunicom.aiAgents.dao.mapper.DmSearchVectorMapper;
import com.chinaunicom.aiAgents.dao.mapper.PortalMapper;
import com.chinaunicom.aiAgents.entity.*;
import com.chinaunicom.common.configuration.ChatModelInfos;
import com.chinaunicom.common.embedding.EmbeddingService;
import com.chinaunicom.common.service.DocParseService;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;


import com.yomahub.liteflow.util.JsonUtil;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.model.output.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import javax.annotation.Resource;
import java.io.IOException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;


@Slf4j
@Service
public class SimilarityDemandAnalysisService {

    private final String prompt = """
            请依据用户输入的需求内容，精准提取需求类型、需求功能总结关键词，并进行总结。输出需严格遵循 JSON 格式，具体如下：
            { 
                "相关系统": ["",""], 
                "技术关键词": ["",""], 
                "业务关键词": ["",""], 
                "功能关键词": ["",""], 
                "需求相关方":  ["",""], 
            }

            说明：
            - [相关系统]：指承接需求，或者需求影响的相关 IT 系统。
            - [技术关键词]：编程语言、框架、工具、协议等（如Java/Kubernetes/OAuth2.0），不超过 5 个。
            - [业务关键词]：领域术语、用户角色、核心流程（如电商/风控/支付对账），不超过 5 个。
            - [功能关键词]：系统模块、操作动作（如用户认证/日志导出/API限流），不超过 5 个。
            - [需求相关方]：业务方、技术团队、用户、合规部门、管理层等利益相关者，不超过 3个。
            """;

    @Resource
    DocParseService docParseService;

    @Resource
    PortalMapper portalMapper;

    @Resource
    DmSearchVectorMapper dmSearchVectorMapper;

    @Resource
    DmInfoMapper dmInfoMapper;

    @Resource
    ChatModelInfos chatModelInfos;

    @Resource
    EmbeddingService embeddingService;

    public SimilarityResp similarityDemandAnalysis(SimilarityRequest similarityRequest) {
        /* 暂时直接页面下载 */
        List<String> fileNames = new ArrayList<>();
        StringBuilder fileContent = new StringBuilder();
        if (!similarityRequest.getRequirementFiles().isEmpty()) {
            List<Map<String, String>> dmFiles = portalMapper.queryDmFiles(similarityRequest.getRequirementFiles().stream()
                    .map(RequirementFilesItem::getKeyname).collect(Collectors.toList()));

            if (!dmFiles.isEmpty()) {
                for (Map<String, String> dmFile : dmFiles) {
                    try {
                        /*pdf中多为图片*/
                        if (dmFile.get("file_name").endsWith(".doc") ||
                                dmFile.get("file_name").endsWith(".docx") ||
                                dmFile.get("file_name").endsWith(".txt")) {
                            fileNames.add(dmFile.get("file_name"));
                            fileContent.append(docParseService.parseDoc(dmFile.get("file_name"),
                                    dmFile.get("key_name")));
                            fileContent.append("\n");
                        }
                    } catch (IOException e) {
                        log.error("{} 下载失败 {}", dmFile.get("url"), e.getMessage());
                    }
                }
            }
        }


        List<ChatMessage> chatMessages = new ArrayList<>();
        chatMessages.add(SystemMessage.systemMessage(prompt));
        if (!fileContent.isEmpty()) {
            chatMessages.add(SystemMessage.systemMessage(fileContent.toString()));
        }

        chatMessages.add(SystemMessage.systemMessage(String.format(
                """
                               需求标题:%s,
                               需求描述:%s
                        """
                , similarityRequest.getRequirementTitle(), similarityRequest.getRequirementDescribe())));

        Response<AiMessage> aiMessageResponse = chatModelInfos.getChatLlm("deepseek-V3-tq-prod").generate(chatMessages);

        DmSearchVector dmSearchVector = getChatMsgs(aiMessageResponse.content().text());
        if (dmSearchVector.getMainKeywords().isEmpty() && dmSearchVector.getSecondKeywords().isEmpty()) {
            throw new RuntimeException("需求内容为空");
        }
        String mainKeywordsVec = embeddingService.getEmbedding(dmSearchVector.getMainKeywords()).toString();
        String secondKeywordsVec = embeddingService.getEmbedding(dmSearchVector.getSecondKeywords()).toString();
        List<Map<String, Object>> searchRes = dmSearchVectorMapper.queryByVector(mainKeywordsVec, secondKeywordsVec, 0.5, 0.5);


        SimilarityResp similarityResp = new SimilarityResp();
        similarityResp.setMax_similarity(Double.parseDouble(searchRes.get(0).get("total_dis").toString()) * 100);
        similarityResp.setSimilar_demands(searchRes.stream().map(item -> {

            Map dmInfo = dmInfoMapper.queryDmInfo(item.get("demand_number").toString());

            SimilarDemandsResp similarDemands = new SimilarDemandsResp();
            similarDemands.setRequirement_no(item.get("demand_number").toString());
            similarDemands.setRequirement_summary(item.get("summary").toString() +
                    parseAiSummaryOriginalDoc(item.get("ai_summary_original_doc").toString()));
            similarDemands.setFiles(String.join(",", fileNames));

            // 格式化相似度得分到小数点后两位
            DecimalFormat df = new DecimalFormat("#.00");
            double similarityScore = Double.parseDouble(item.get("total_dis").toString()) * 100;
            similarDemands.setSimilarity_score(Double.parseDouble(df.format(similarityScore)));

            Meta meta = new Meta();

            if (dmInfo != null) {
                similarDemands.setRequirement_title(dmInfo.get("dm_name").toString());
                similarDemands.setRequirement_desc(dmInfo.get("dm_desc").toString());
                meta.setProcess_instance_id(dmInfo.get("process_instance_id").toString());
                meta.setDm_status(dmInfo.get("dm_status").toString());
                meta.setProfessional_line_name(dmInfo.get("professional_line_name").toString());
                meta.setDm_requester_name(dmInfo.get("dm_requester_name").toString());
            }
            // meta.setFile_summary();
            similarDemands.setMeta(meta);
            return similarDemands;
        }).collect(Collectors.toList()));

        log.info("""
                        待比较需求：{}
                        提炼结果：{}, {}
                        搜索的相似需求：{}
                        """,
                similarityRequest.getRequirementTitle(),
                dmSearchVector.getMainKeywords(), dmSearchVector.getSecondKeywords(),
                similarityResp.getSimilar_demands());
        return similarityResp;
    }

    /*{
    "名称":"暂停eSIM卡结算",
    "相关系统": ["联通伴侣定位器管理系统", "业务结算系统"],
    "技术关键词": ["eSIM卡", "业务结算", "13位代码"],
    "业务关键词": ["市场部", "结算规则", "全国业务"],
    "功能关键词": ["结算暂停", "规则调整", "业务暂停"],
    "需求相关方":  ["集团市场部", "业务结算团队", "部门管理层"],
    "总结": "为实现暂停联通伴侣定位器产品JQ121-13位eSIM卡业务结算的目标，需在全国范围内对结算系统进行规则调整，优先级为高，涉及联通伴侣定位器管理系统和业务结算系统，关键依赖市场部确认后执行。"
    }*/
    private String parseAiSummaryOriginalDoc(String summaryJson) {

        JsonNode jsonNode = JsonUtil.parseObject(summaryJson);


        // 提取 JSON 数组解析逻辑
        String relatedSystems = parseJsonArray(jsonNode, "相关系统");
        String keywords1 = parseJsonArray(jsonNode, "技术关键词");
        String keywords2 = parseJsonArray(jsonNode, "业务关键词");
        String keywords3 = parseJsonArray(jsonNode, "功能关键词");
        String relatedDemanders = parseJsonArray(jsonNode, "需求相关方");

        return String.format("""
                <br>
                - **相关系统**: %s
                - **技术关键词**: %s
                - **业务关键词**: %s
                - **功能关键词**: %s
                - **需求相关方**: %s
                """, relatedSystems, keywords1, keywords2, keywords3, relatedDemanders);

    }

    // 提取 JSON 数组解析逻辑
    private String parseJsonArray(JsonNode json, String key) {
        JsonNode arrayNode = json.get(key);
        if (arrayNode != null && arrayNode.isArray()) {
            return StreamSupport.stream(arrayNode.spliterator(), false)
                    .map(JsonNode::asText)
                    .collect(Collectors.joining(","));
        }
        return "";
    }

    private DmSearchVector getChatMsgs(String resp) {
        DmSearchVector dmSearchVector = new DmSearchVector();

        String ans = resp.replaceAll("```json", "")
                .replaceAll("```", "")
                .replaceAll("“", "\"")
                .trim();
        dmSearchVector.setAiSummaryOriginalDoc(ans);
        try {
            JsonObject json = JsonParser.parseString(ans).getAsJsonObject();
            String relatedSystems = json.getAsJsonArray("相关系统").asList()
                    .stream()
                    .map(JsonElement::getAsString).collect(Collectors.joining(","));
            String keywords1 = json.getAsJsonArray("技术关键词").asList()
                    .stream()
                    .map(JsonElement::getAsString).collect(Collectors.joining(","));
            String keywords2 = json.getAsJsonArray("业务关键词").asList()
                    .stream()
                    .map(JsonElement::getAsString).collect(Collectors.joining(","));
            String keywords3 = json.getAsJsonArray("功能关键词").asList()
                    .stream()
                    .map(JsonElement::getAsString).collect(Collectors.joining(","));
            String relatedDemanders = json.getAsJsonArray("需求相关方").asList()
                    .stream()
                    .map(JsonElement::getAsString).collect(Collectors.joining(","));
//            String summary = json.get("总结").getAsString();
            dmSearchVector.setMainKeywords(relatedSystems + ";;" + relatedDemanders);
            dmSearchVector.setSecondKeywords(keywords1 + ";;" + keywords2 + ";;" + keywords3);
        } catch (JsonSyntaxException ex) {
            log.error("JSON解析错误: {}", ex.getMessage());
        }

        return dmSearchVector;
    }
}
