package com.chinaunicom.aiAgents.service.impl;


import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinaunicom.aiAgents.dao.domain.AiPendingReview;
import com.chinaunicom.aiAgents.dao.domain.AiPendingSummaryConfig;
import com.chinaunicom.aiAgents.dao.mapper.AiPendingReviewMapper;
import com.chinaunicom.aiAgents.dao.mapper.AiPendingSummaryConfigMapper;
import com.chinaunicom.aiAgents.dao.mapper.UserInfoMapper;
import com.chinaunicom.aiAgents.service.ProjectAssistantService;
import com.chinaunicom.common.entity.uniRequest.QueryInfo;
import com.chinaunicom.common.flow.ProjectQueryInfo;
import com.chinaunicom.common.flow.ThinkingStage;
import com.chinaunicom.common.flow.WaitingLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 立项助手相关业务处理类
 */
@Service
@Slf4j
public class ProjectAssistantServiceImpl implements ProjectAssistantService {

    @Resource
    AiPendingReviewMapper aiPendingReviewMapper;

    @Resource
    AiPendingSummaryConfigMapper aiPendingSummaryConfigMapper;

    @Resource
    UserInfoMapper userInfoMapper;

    /**
     * 立项助手V2,agent_id:01acd9a0e640576dbf4310ff285bbe50
     * 场景一：小研代办：    默认问题  思维链模式+思维链输出    背景知识：附件，页面，项目历史数据
     * 场景二：小研立项助手：用户问题   非思维链模式，调text2sql流式接口，背景知识：项目历史数据
     * 场景三：立项专家检查：默认问题   思维链模式+思维链输出    背景知识：附件，页面，项目历史数据
     * 场景四：项目经理自查：默认问题   思维链模式+思维链输出    背景知识：附件，页面，项目历史数据
     * 场景五：立项页面问答：用户问题   思维链模式但不输出思维链  背景知识：附件，页面，项目历史数据
     *
     * @param sceneNo   待办请求信息
     * @param queryInfo 问题的相关业务参数
     * @return
     */

    @Override
    public List<ThinkingStage> queryRagInfo(int sceneNo, QueryInfo queryInfo) {
        Map userMap = userInfoMapper.queryUserById(queryInfo.getUserId());
        List<ThinkingStage> thinkingStages = new ArrayList<>();
        List<ProjectQueryInfo> queryList = new ArrayList<>();
        Map<String, Object> backgroundInfo = queryInfo.getBackgroundInfo();
        List<String> fileKeys = null;
        switch (sceneNo) {
            case 1://小研代办
                //从ai_pending_review拿到背景知识
                AiPendingReview pendingReview = aiPendingReviewMapper.selectOne(new LambdaQueryWrapper<AiPendingReview>().eq(AiPendingReview::getId, queryInfo.getPendingId()));
                //再去ai_pending_summary_config查询预置问题
                if (pendingReview == null) {

                    thinkingStages.add(ThinkingStage.builder()
                            .question("""
                                    告诉用户：当前待办不存在。
                                    """)
                            .build()
                    );
                    return thinkingStages;
                }
                backgroundInfo = JSONObject.parseObject(pendingReview.getBackgroundInfo(), Map.class);
                List<AiPendingSummaryConfig> configList = aiPendingSummaryConfigMapper.selectList(new LambdaQueryWrapper<AiPendingSummaryConfig>()
                        .eq(AiPendingSummaryConfig::getMode, pendingReview.getPendModel())
                        .eq(AiPendingSummaryConfig::getFunc, pendingReview.getPendFun())
                        .eq(AiPendingSummaryConfig::getIsOpen, 1)
                        .eq(AiPendingSummaryConfig::getAct, pendingReview.getPendAct()));
                if (ObjectUtils.isEmpty(configList)) {
                    thinkingStages.add(ThinkingStage.builder()
                            .question("""
                                    告诉用户：当前代办节点无预置问题。
                                    """)
                            .build()
                    );
                    return thinkingStages;
                }
                for (AiPendingSummaryConfig config : configList) {
                    ProjectQueryInfo projectQueryInfo = new ProjectQueryInfo();
                    projectQueryInfo.setProjectId(MapUtils.getString(backgroundInfo, "projectId"));
                    projectQueryInfo.setProjectName(MapUtils.getString(backgroundInfo, "projectName"));
                    projectQueryInfo.setQuery(config.getSummaryNote());
                    projectQueryInfo.setMode(config.getMode());
                    projectQueryInfo.setFunc(config.getFunc());
                    projectQueryInfo.setAct(config.getAct());
                    projectQueryInfo.setAnswerNote(config.getAnswerNote());
                    projectQueryInfo.setSceneNo(sceneNo);
                    projectQueryInfo.setCreateId(MapUtils.getString(userMap, "email"));
                    projectQueryInfo.setCreateName(MapUtils.getString(userMap, "name"));
                    if (backgroundInfo.containsKey("fileKeys") && backgroundInfo.get("fileKeys") instanceof List) {
                        fileKeys = (List<String>) backgroundInfo.get("fileKeys");
                        projectQueryInfo.setFileKeys(fileKeys);
                    }
                    projectQueryInfo.setPageInfo(backgroundInfo);
                    queryList.add(projectQueryInfo);
                }
                backgroundInfo.remove("fileKeys");
                backgroundInfo.remove("projectId");
                backgroundInfo.remove("projectName");
                backgroundInfo.remove("mode");
                backgroundInfo.remove("func");
                backgroundInfo.remove("act");
                break;
            case 2: //小研立项助手实时问答
                /**没有附件也没有表单信息，直接text2sql获取背景知识
                 * 接口：text2sql/project/api
                 * POST请求
                 * application/json
                 * 参数：
                 * {
                 *     "user_id":"28164732462",
                 *     "chat_id":"276234324732432",
                 *     "query":"数字化研发平台在2024年自研投资占比是多少"
                 */
                ProjectQueryInfo projectQueryInfo = new ProjectQueryInfo();
                projectQueryInfo.setQuery(queryInfo.getQuery());
                projectQueryInfo.setSceneNo(sceneNo);
                projectQueryInfo.setCreateId(MapUtils.getString(userMap, "email"));
                projectQueryInfo.setCreateName(MapUtils.getString(userMap, "name"));
                queryList.add(projectQueryInfo);
                break;
            case 3: //立项专家检查
                configList = aiPendingSummaryConfigMapper.selectList(new LambdaQueryWrapper<AiPendingSummaryConfig>()
                        .eq(AiPendingSummaryConfig::getMode, MapUtils.getString(queryInfo.getBackgroundInfo(), "mode"))
                        .eq(AiPendingSummaryConfig::getFunc, MapUtils.getString(queryInfo.getBackgroundInfo(), "func"))
                        .eq(AiPendingSummaryConfig::getIsOpen, 1)
                        .eq(AiPendingSummaryConfig::getAct, MapUtils.getString(queryInfo.getBackgroundInfo(), "act")));
                if (ObjectUtils.isEmpty(configList)) {
                    thinkingStages.add(ThinkingStage.builder()
                            .question("""
                                    告诉用户：当前评审节点无预置问题。
                                    """)
                            .build()
                    );
                    return thinkingStages;
                }
                for (AiPendingSummaryConfig config : configList) {
                    projectQueryInfo = new ProjectQueryInfo();
                    projectQueryInfo.setProjectId(MapUtils.getString(backgroundInfo, "projectId"));
                    projectQueryInfo.setProjectName(MapUtils.getString(backgroundInfo, "projectName"));
                    projectQueryInfo.setQuery(config.getSummaryNote());
                    projectQueryInfo.setMode(config.getMode());
                    projectQueryInfo.setFunc(config.getFunc());
                    projectQueryInfo.setAct(config.getAct());
                    projectQueryInfo.setSceneNo(sceneNo);
                    projectQueryInfo.setCreateId(MapUtils.getString(userMap, "email"));
                    projectQueryInfo.setCreateName(MapUtils.getString(userMap, "name"));
                    projectQueryInfo.setAnswerNote(config.getAnswerNote());
                    if (backgroundInfo.containsKey("fileKeys") && backgroundInfo.get("fileKeys") instanceof List) {
                        fileKeys = (List<String>) backgroundInfo.get("fileKeys");
                        projectQueryInfo.setFileKeys(fileKeys);
                    }
                    projectQueryInfo.setPageInfo(backgroundInfo);
                    queryList.add(projectQueryInfo);
                }
                backgroundInfo.remove("fileKeys");
                backgroundInfo.remove("projectId");
                backgroundInfo.remove("projectName");
                backgroundInfo.remove("mode");
                backgroundInfo.remove("func");
                backgroundInfo.remove("act");
                break;
            case 4: //项目经理自查，全量默认问题问答
                configList = aiPendingSummaryConfigMapper.selectList(new LambdaQueryWrapper<AiPendingSummaryConfig>()
                        .eq(AiPendingSummaryConfig::getMode, MapUtils.getString(queryInfo.getBackgroundInfo(), "mode"))
                        .eq(AiPendingSummaryConfig::getIsOpen, 1)
                        .eq(AiPendingSummaryConfig::getFunc, MapUtils.getString(queryInfo.getBackgroundInfo(), "func")));
                if (ObjectUtils.isEmpty(configList)) {
                    thinkingStages.add(ThinkingStage.builder()
                            .question("""
                                    告诉用户：当前流程无预置问题。
                                    """)
                            .build()
                    );
                    return thinkingStages;
                }
                for (AiPendingSummaryConfig config : configList) {
                    projectQueryInfo = new ProjectQueryInfo();
                    projectQueryInfo.setProjectId(MapUtils.getString(backgroundInfo, "projectId"));
                    projectQueryInfo.setProjectName(MapUtils.getString(backgroundInfo, "projectName"));
                    projectQueryInfo.setQuery(config.getSummaryNote());
                    projectQueryInfo.setMode(config.getMode());
                    projectQueryInfo.setFunc(config.getFunc());
                    projectQueryInfo.setAct(config.getAct());
                    projectQueryInfo.setSceneNo(sceneNo);
                    projectQueryInfo.setCreateId(MapUtils.getString(userMap, "email"));
                    projectQueryInfo.setCreateName(MapUtils.getString(userMap, "name"));
                    projectQueryInfo.setAnswerNote(config.getAnswerNote());
                    if (backgroundInfo.containsKey("fileKeys") && backgroundInfo.get("fileKeys") instanceof List) {
                        fileKeys = (List<String>) backgroundInfo.get("fileKeys");
                        projectQueryInfo.setFileKeys(fileKeys);
                    }
                    projectQueryInfo.setPageInfo(backgroundInfo);
                    queryList.add(projectQueryInfo);
                }
                backgroundInfo.remove("fileKeys");
                backgroundInfo.remove("projectId");
                backgroundInfo.remove("projectName");
                backgroundInfo.remove("mode");
                backgroundInfo.remove("func");
                backgroundInfo.remove("act");
                break;
            case 5: //评审环节专家实时问答
                projectQueryInfo = new ProjectQueryInfo();
                projectQueryInfo.setProjectId(MapUtils.getString(backgroundInfo, "projectId"));
                projectQueryInfo.setProjectName(MapUtils.getString(backgroundInfo, "projectName"));
                projectQueryInfo.setQuery(queryInfo.getQuery());
                projectQueryInfo.setMode(MapUtils.getString(backgroundInfo, "model"));
                projectQueryInfo.setFunc(MapUtils.getString(backgroundInfo, "func"));
                projectQueryInfo.setAct(MapUtils.getString(backgroundInfo, "act"));
                projectQueryInfo.setSceneNo(sceneNo);
                projectQueryInfo.setCreateId(MapUtils.getString(userMap, "email"));
                projectQueryInfo.setCreateName(MapUtils.getString(userMap, "name"));
                if (backgroundInfo.containsKey("fileKeys") && backgroundInfo.get("fileKeys") instanceof List) {
                    fileKeys = (List<String>) backgroundInfo.get("fileKeys");
                    projectQueryInfo.setFileKeys(fileKeys);
                }
                backgroundInfo.remove("fileKeys");
                backgroundInfo.remove("projectId");
                backgroundInfo.remove("projectName");
                backgroundInfo.remove("mode");
                backgroundInfo.remove("func");
                backgroundInfo.remove("act");
                projectQueryInfo.setPageInfo(backgroundInfo);
                queryList.add(projectQueryInfo);
                break;
            default:
                break;
        }
        for (ProjectQueryInfo queryItem : queryList) {
            thinkingStages.add(ThinkingStage.builder()
                    .question(queryItem.getQuery()).projectQueryInfo(queryItem)
                    .parallelLock(new WaitingLock())
                    .build()
            );
        }
        log.info("接口，响应内容:{}", thinkingStages);
        return thinkingStages;
    }

}
