package com.chinaunicom.aiAgents.service.impl;



import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.chinaunicom.aiAgents.dao.domain.DmAgentTaskInfo;
import com.chinaunicom.aiAgents.dao.mapper.DmAgentTaskInfoMapper;
import com.chinaunicom.aiAgents.entity.*;
import com.chinaunicom.aiAgents.service.HttpClientService;
import com.chinaunicom.aiAgents.service.LargeModelService;
import com.chinaunicom.aiAgents.service.demandAssistant.DemandTaskService;
import com.chinaunicom.aiAgents.service.demandAssistant.PendingReviewService;
import com.chinaunicom.aiAgents.utils.ReturnResult;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
public class DemandTaskServiceImpl implements DemandTaskService {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${dm.intelligent.research.url:https://10.124.151.16:19530/dev-api/robot/question-answer-sso}")
    private String intelligentResearchUrl;

    @Resource
    OkHttpClient okHttpClient;

    @Autowired
    private LargeModelService largeModelService;
    
    @Autowired
    private HttpClientService httpClientService;
    
    @Autowired
    private DmAgentTaskInfoMapper dmAgentTaskInfoMapper;
    @Autowired
    private PendingReviewService pendingReviewService;

    /**
     * 小研待办新建接口
     * 对应Python中的xiaoyan_task_sender函数
     */
    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public ReturnResult<DataModel> addXiaoYanTaskSender(PendingItem request) {
        log.info("收到 addXiaoYanTaskSender 请求: {}", request);
        
        try {
            // 第一步：调用大模型处理（目前只是透传）
            PendingItem processedResult = largeModelService.processWithLargeModel(request);

            // 第二步：将原始数据转发给 小研 接口
            // 将PendingItem转换为Map参数
            Map<String, Object> params = new HashMap<>();
            params.put("title", processedResult.getTitle());
            params.put("url", processedResult.getUrl());
            params.put("creatorEmail", processedResult.getCreator());
            params.put("responsibleList", processedResult.getResponsibleList());
            params.put("processInstanceId", processedResult.getProcessInstanceId());
            params.put("processName", processedResult.getProcessName());
            params.put("dmNumber", processedResult.getDmNumber());
            params.put("taskName", processedResult.getTaskName());
            // TODO backgroundInfo 暂时给一个空map
            params.put("backgroundInfo", new HashMap<>());

            // TODO 以下参数小妍是否有用，有用放到上面
            params.put("type", processedResult.getType());
            params.put("countersign", processedResult.getCountersign());
            params.put("belongModule", processedResult.getBelongModule());
            params.put("stateShow", processedResult.getStateShow());
            params.put("aisummary", processedResult.getAisummary());
            params.put("taskId", processedResult.getTaskId());

            ResultMessage response = pendingReviewService.xqCreatePending(params);
            log.info("收到 小研 方法返回值: {}", objectMapper.writeValueAsString(response));
            if (response != null && Integer.valueOf(200).equals(response.getCode())) {
                // 第三步：存储数据
                try {
                    Object result = response.getData();
                    DataModel data = objectMapper.convertValue(result, DataModel.class);
                    String pendingId = data.getPendingId();
                    //保存小研待办数据到dm_agent_task_info
                    createTaskInfo(request, pendingId);

                    return ReturnResult.success(data);
                } catch (Exception e) {
                    log.error("保存新建待办数据失败！: {}", e.getMessage(), e);
                    return ReturnResult.error("保存新建待办数据失败: " + e.getMessage());
                }
            } else {
                return ReturnResult.error("提交到 ai_sa 失败");
            }
        } catch (Exception e) {
            log.error("处理 XiaoYanTaskSender 请求失败: {}", e.getMessage(), e);
            return ReturnResult.error("内部服务器错误: " + e.getMessage());
        }
    }
    
    /**
     * 小研待办更新接口
     * 对应Python中的xiaoyan_task_sender函数（更新状态）
     */
    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public ReturnResult<String> updateXiaoYanTaskStatus(PendingItem request) {
        log.info("收到 updateXiaoYanTaskStatus 请求: {}", request);
        
        try {
            // 获取task_id
            String taskId = request.getTaskId();
            if (StringUtils.isEmpty(taskId)) {
                return ReturnResult.of(400, "缺少 task_id");
            }
            
            // 2. 查询数据库任务
            DmAgentTaskInfo dbTask = dmAgentTaskInfoMapper.selectByTaskId(taskId);
            
            if (dbTask == null) {
                return ReturnResult.of(404, "任务未找到");
            }
            
            // 4. 发送请求到外部服务
            boolean updateSuccess = httpClientService.updatePendingReview(
                    dbTask.getPendingId(), 
                    dbTask.getTaskHandler(), 
                    1, 
                    "完成"
            );
            
            if (!updateSuccess) {
                return ReturnResult.of(502, "外部服务返回错误");
            }
            
            // 5. 更新数据库任务状态
            updateTaskInfo(taskId);
            
            // 6. 返回成功响应
            return ReturnResult.success("状态更新成功");
            
        } catch (Exception e) {
            log.error("处理请求失败: {}", e.getMessage(), e);
            return ReturnResult.error("内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 保存小研待办数据到dm_agent_task_info
     * 对应Python中的create_task_info函数
     */
    public void createTaskInfo(PendingItem pendingItem, String pendingId) {
        try {
            DmAgentTaskInfo dbTask = new DmAgentTaskInfo();
            dbTask.setId(UUID.randomUUID().toString().replace("-", ""));
            dbTask.setPendingId(pendingId);
            dbTask.setTaskId(pendingItem.getTaskId());
            dbTask.setTaskTitle(pendingItem.getTitle());
            dbTask.setAiSummary(pendingItem.getAisummary());
            // 对应Python中的stateShow，这里设为0表示新建状态
            dbTask.setTaskState(0);
            dbTask.setUrl(pendingItem.getUrl());
            dbTask.setCreateTime(new Date());
            dbTask.setUpdateTime(new Date());
            dbTask.setDmNumber(pendingItem.getDmNumber());
            dbTask.setProcessInstanceId(pendingItem.getProcessInstanceId());
            dbTask.setTaskCreator(pendingItem.getCreator());
            
            // 设置任务处理人（取责任人列表的第一个）
            if (pendingItem.getResponsibleList() != null && !pendingItem.getResponsibleList().isEmpty()) {
                dbTask.setTaskHandler(pendingItem.getResponsibleList().get(0));
            }
            log.info("保存任务信息前");
            dmAgentTaskInfoMapper.insertDmAgentTaskInfo(dbTask);
            log.info("成功保存任务信息，任务ID: {}", dbTask.getId());
        } catch (Exception e) {
            log.error("保存任务信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存任务信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新任务信息
     * 对应Python中的update_task_info函数
     */
    public void updateTaskInfo(String taskId) {
        try {
            DmAgentTaskInfo dbTask = dmAgentTaskInfoMapper.selectByTaskId(taskId);

            if (dbTask == null) {
                throw new RuntimeException("No task info found for task_id: " + taskId);
            }
            
            // 更新任务信息字段
            // 设置为完成状态
            dbTask.setTaskState(1);
            dbTask.setUpdateTime(new Date());
            
            int updateResult = dmAgentTaskInfoMapper.updateStateById(dbTask);
            if (updateResult <= 0) {
                log.error("数据库更新失败");
                throw new RuntimeException("数据库更新失败");
            }
            log.info("成功更新任务状态，任务ID: {}", taskId);
        } catch (Exception e) {
            log.error("更新任务信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("更新任务信息失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnResult<Map<String, Object>> callSsoRedirect(CallSsoRedirectReq request) {
        // 获取智搜token
        String token;
        try {
            BpcUser userInfo = httpClientService.getBpcCurrentUserInfo(request.getBpcAuthorization());

            Map<String, String> authData = new HashMap<>();
            authData.put("loginName", userInfo.getUserAccount());
            authData.put("userName", userInfo.getUserName());
            authData.put("email", userInfo.getEmail());
            authData.put("phoneNumber", userInfo.getMobile());
            authData.put("organizationCode", userInfo.getDeptID());
            authData.put("organizationName", userInfo.getDeptName());

            log.info("接口请求参数auth_data:{}", authData);
            token = httpClientService.getZhiSouToken(authData);
        } catch (Exception e) {
            log.error("获取智搜token时发生错误: ", e);
            return ReturnResult.error("获取智搜token时发生错误: " + e.getMessage());
        }
        // 获取智搜重定向页面
        String zhiSouRedirect = httpClientService.getZhiSouRedirect(Map.of(
                "token", token,
                "sessionUuid", request.getConversationId(),
                "messageUuid", request.getChatId()
        ));
        // 返回结果
        return ReturnResult.success(Map.of(
                "accessToken", token,
                "html", zhiSouRedirect
        ));
    }

    @Override
    public SseEmitter demandRouter(DemandRouterVo request) {
        SseEmitter emitter = new SseEmitter();
        // 异步处理请求
        CompletableFuture.runAsync(() -> {
            try {
//                processRequest(request, emitter);
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
        });
        return emitter;
    }
/*
    private void processRequest(DemandRouterVo request, SseEmitter emitter) {
        String userInput = request.getQueryInfo().getQuery();
        String userId = request.getQueryInfo().getUserId();
        String conversationId = request.getQueryInfo().getConversationId();
        String chatId = request.getQueryInfo().getChatId() != null ?
                request.getQueryInfo().getChatId() : UUID.randomUUID().toString();
        Map<String, Object> quote = request.getQueryInfo().getQuote();
        Map<String, Object> backgroundInfo = request.getQueryInfo().getBackgroundInfo();

        // 处理工具信息
        Map<String, Object> toolInfo = new HashMap<>();
        toolInfo.put("name", "none");
        toolInfo.put("parameters", new HashMap<>());

        if (request.getQueryInfo().getToolInfo() != null &&
                !request.getQueryInfo().getToolInfo().trim().isEmpty()) {
            toolInfo.put("name", request.getQueryInfo().getToolInfo());
            toolInfo.put("parameters", Map.of("query", userInput));
        }

        // 处理historyInfo，转换为适当的消息格式
        List<Map<String, Object>> historyMessages = new ArrayList<>();
        if (request.getHistoryInfo() != null) {
            // request.historyInfo是从最新到最旧排序的，需要先反转为从最旧到最新
            // 每个item已经是一对human-ai消息
            for (int i = request.getHistoryInfo().size() - 1; i >= 0; i--) {
                DemandRouterVo.HistoryItem item = request.getHistoryInfo().get(i);

                // 处理human消息

                String humanContent;
                Map<String, Object> humanToolInfo = new HashMap<>();
                humanToolInfo.put("name", "none");
                humanToolInfo.put("parameters", new HashMap<>());

                if (item.getHuman() instanceof String) {
                    humanContent = (String) item.getHuman();
                } else {
                    Map<String, Object> humanMap = (Map<String, Object>) item.getHuman();
                    humanContent = (String) humanMap.get("content");
                    if (humanMap.containsKey("tool_info")) {
                        humanToolInfo = (Map<String, Object>) humanMap.get("tool_info");
                    }
                }

                // 创建human消息并添加到历史记录
                Map<String, Object> humanMsg = new HashMap<>();
                humanMsg.put("content", humanContent);
                humanMsg.put("metadata", Map.of("tool_info", humanToolInfo));
                humanMsg.put("role", "human");
                historyMessages.add(humanMsg);

                // 处理AI消息
                Map<String, Object> aiMsg = new HashMap<>();
                String aiContent;

                if (item.getAi() instanceof String) {
                    aiContent = (String) item.getAi();
                } else {
                    Map<String, Object> aiMap = (Map<String, Object>) item.getAi();
                    aiContent = (String) aiMap.get("content");
                }

                aiMsg.put("content", aiContent);
                aiMsg.put("role", "ai");
                historyMessages.add(aiMsg);
            }
        }

        // 执行意图识别
        Map<String, Object> conversationResult = runConversation(userInput, historyMessages, toolInfo);

        boolean isAskForParameters = (boolean) conversationResult.getOrDefault(
                "is_ask_for_parameters", false);
        boolean functionIsStream = (boolean) conversationResult.getOrDefault(
                "function_is_stream", true);

        if (isAskForParameters) {
            List<Map<String, Object>> promptMessages = (List<Map<String, Object>>)
                    conversationResult.getOrDefault("llm_prompt_messages", new ArrayList<>());
            streamLLMResponse(promptMessages, emitter);
        } else if (functionIsStream) {
            // 处理流式工具调用
            Map<String, Object> currentFunction = (Map<String, Object>)
                    conversationResult.getOrDefault("current_function",
                            Map.of("name", "none", "parameters", new HashMap<>()));

            String toolName = (String) currentFunction.get("name");
            Map<String, Object> toolParams = (Map<String, Object>)
                    currentFunction.get("parameters");

            if (toolName.equals("none")) {
                sendErrorResponse(emitter, "未指定工具", 400);
                return;
            }

            // 添加额外参数
            toolParams.put("background_info", backgroundInfo);
            toolParams.put("chat_id", chatId);
            toolParams.put("conversation_id", conversationId);

            streamToolResponse(toolName, toolParams, emitter);
        } else {
            List<Map<String, Object>> promptMessages = (List<Map<String, Object>>)
                    conversationResult.getOrDefault("llm_prompt_messages", new ArrayList<>());
            streamLLMResponse(promptMessages, emitter);
        }
    }

    private Map<String, Object> runConversation(String userInput, List<Map<String, Object>> historyMessages, Map<String, Object> toolInfo) {
        long startTime = System.currentTimeMillis();
        log.info("开始新的对话，用户输入: {}...",
                userInput.length() > 50 ? userInput.substring(0, 50) : userInput);

        // 创建当前消息列表的副本
        List<Map<String, Object>> currentMessages = historyMessages != null ?
                new ArrayList<>(historyMessages) : new ArrayList<>();

        // 创建用户消息
        Map<String, Object> userMessage = new HashMap<>();
        userMessage.put("content", userInput);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("tool_info", toolInfo);
        userMessage.put("response_metadata", metadata);

        log.info("from runConversation userMessage: {}", userMessage);
        currentMessages.add(userMessage);

        // 创建初始状态
        AgentState currentState = new AgentState(
                currentMessages,
                new ArrayList<>(),
                new ArrayList<>(),
                new ArrayList<>(),
                false,
                false,
                new ArrayList<>(),
                null,
                false,
                false,
                null,
                new HashMap<>(),
                startTime
        );

        log.debug("初始状态: is_done={}", currentState.isDone());

        // 异步执行
        return CompletableFuture.supplyAsync(() -> {
            try {
                Map<String, Object> config = new HashMap<>();
                config.put("recursion_limit", 10);
                log.info("推进 LangGraph 到最终节点...");

                // todo agent_graph.invoke
                Map<String, Object> result = agentGraph.invoke(currentState, config);
                log.debug("type(result): {}", result.getClass().getName());
                return result;
            } catch (Exception e) {
                log.error("工作流执行错误: {}", e.getMessage(), e);
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("llm_prompt_messages", new ArrayList<>());
                errorResult.put("messages", currentMessages);
                return errorResult;
            }
        });
    }

    private void streamLLMResponse(List<Map<String, Object>> promptMessages, SseEmitter emitter) {
        try {
            if (promptMessages == null || promptMessages.isEmpty()) {
                Map<String, Object> errorResponse = Map.of(
                        "code", 400,
                        "message", "未生成有效的模型输入prompt，无法流式回复",
                        "data", Map.of(
                                "content", "系统未能生成有效回复，请重试。",
                                "think", ""
                        )
                );
                emitter.send(SseEmitter.event().data(objectMapper.writeValueAsString(errorResponse)));
                return;
            }

            log.info("stream_response prompt_messages: {}", promptMessages);
            StringBuilder buffer = new StringBuilder();
            boolean firstChunkReceived = false;
            long ttfbStartTime = System.currentTimeMillis();

            // todo
            StreamGenerator responseLLM = new StreamGenerator(); // 需要根据实际情况实现

            for (Object chunk : responseLLM.generateStream(promptMessages)) {
                log.debug("stream_response chunk: {}", chunk);

                if (chunk instanceof String && ((String) chunk).startsWith("data: ")) {
                    // 直接传递已经格式化好的SSE数据
                    emitter.send(SseEmitter.event().data(chunk));

                    if (!firstChunkReceived) {
                        long ttfb = System.currentTimeMillis() - ttfbStartTime;
                        log.info("TTFB: {} seconds", ttfb / 1000.0);
                        firstChunkReceived = true;
                    }
                } else if (chunk instanceof ResponseChunk && ((ResponseChunk) chunk).getContent() != null) {
                    if (!firstChunkReceived) {
                        long ttfb = System.currentTimeMillis() - ttfbStartTime;
                        log.info("TTFB: {} seconds", ttfb / 1000.0);
                        firstChunkReceived = true;
                    }

                    buffer.append(((ResponseChunk) chunk).getContent());
                    Map<String, Object> responseData = Map.of(
                            "code", 200,
                            "message", "success",
                            "data", Map.of(
                                    "content", buffer.toString(),
                                    "think", ""
                            )
                    );
                    emitter.send(SseEmitter.event().data("data: " + objectMapper.writeValueAsString(responseData) + "\n\n"));
                }

                // 发送结束标记
                if (!(chunk instanceof String && ((String) chunk).endsWith("[DONE]\n\n"))) {
                    emitter.send(SseEmitter.event().data("data: [DONE]\n\n"));
                }

                try {
                    TimeUnit.MILLISECONDS.sleep(0); // 模拟Python的asyncio.sleep(0)
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            if (buffer.length() > 0) {
                log.debug("final buffer: {}", buffer);
            }
        } catch (Exception e) {
            log.error("流式处理异常: ", e);
            try {
                Map<String, Object> errorResponse = Map.of(
                        "code", 500,
                        "message", "处理请求时发生错误",
                        "data", Map.of(
                                "content", "处理请求时发生错误: " + e.getMessage(),
                                "think", ""
                        )
                );
                emitter.send(SseEmitter.event().data("data: " + objectMapper.writeValueAsString(errorResponse) + "\n\n"));
                emitter.send(SseEmitter.event().data("data: [DONE]\n\n"));
            } catch (IOException ioException) {
                log.error("发送错误响应时发生异常: ", ioException);
            }
        } finally {
            emitter.complete();
        }
    }

    private void streamToolResponse(String toolName, Map<String, Object> params, SseEmitter emitter) {
        try {
            StringBuilder buffer = new StringBuilder();
            StringBuilder thinkBuffer = new StringBuilder();
            boolean inThink = false;
            boolean contentStarted = false;
            boolean firstChunkReceived = false;
            long ttfbStartTime = System.currentTimeMillis();
            Object lastChunk = null;

            // todo 调用工具
            ToolStreamGenerator toolFunc = new ToolStreamGenerator(); // 需实现实际工具调用逻辑
            for (Object chunk : toolFunc.generate(toolName, params)) {
                lastChunk = chunk;

                if (chunk instanceof String chunkStr) {

                    if (chunkStr.startsWith("data: ")) {
                        emitter.send(SseEmitter.event().data(chunkStr));
                    } else {
                        // 如果调的是智搜，特殊处理
                        if ("intelligent_research_request_api".equals(toolName)) {
                            if (chunkStr.contains("</think>") && !contentStarted) {
                                // 首次遇到 toModel，从此开始记录 content
                                contentStarted = true;

                                // 提取 toModel 后面的内容
                                String[] parts = chunkStr.split("</think>", 2);
                                if (parts.length > 1) {
                                    buffer.append(parts[1]);
                                }
                                inThink = false;
                            } else if (chunkStr.contains("<think>")) {
                                // 切换到 think 模式
                                String[] contentParts = chunkStr.split("<think>", 2);
                                if (contentStarted && contentParts[0].length() > 0) {
                                    buffer.append(contentParts[0]);
                                }
                                inThink = true;
                                if (contentParts.length > 1) {
                                    thinkBuffer.append(contentParts[1]);
                                }
                            } else if (chunkStr.contains("</think>")) {
                                // 已经开始记录了，继续拼接
                                String[] contentParts = chunkStr.split("</think>", 2);
                                if (contentParts.length > 1) {
                                    buffer.append(contentParts[1]);
                                }
                            } else {
                                if (inThink) {
                                    thinkBuffer.append(chunkStr);
                                } else if (contentStarted && !chunkStr.contains("{\"summary_flag\":\"end\"}")) {
                                    buffer.append(chunkStr);
                                }
                            }

                            Map<String, Object> responseData = Map.of(
                                    "code", 200,
                                    "message", "success",
                                    "data", Map.of(
                                            "content", buffer.toString(),
                                            "think", thinkBuffer.toString()
                                    )
                            );
                            emitter.send(SseEmitter.event().data("data: " +
                                    objectMapper.writeValueAsString(responseData) + "\n\n"));
                        } else {
                            log.info("输出公研大模型能力响应结果");
                            buffer.append(chunkStr);
                            Map<String, Object> responseData = Map.of(
                                    "code", 200,
                                    "message", "success",
                                    "data", Map.of(
                                            "content", buffer.toString(),
                                            "think", ""
                                    )
                            );
                            emitter.send(SseEmitter.event().data("data: " +
                                    objectMapper.writeValueAsString(responseData) + "\n\n"));
                        }
                    }

                    if (!firstChunkReceived) {
                        long ttfb = System.currentTimeMillis() - ttfbStartTime;
                        log.info("TTFB: {} seconds", ttfb / 1000.0);
                        firstChunkReceived = true;
                    }
                }

                try {
                    TimeUnit.MILLISECONDS.sleep(0); // 模拟Python的asyncio.sleep(0)
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            // 处理流结束标记
            if (lastChunk == null) {
                if (firstChunkReceived) {
                    emitter.send(SseEmitter.event().data("data: [DONE]\n\n"));
                }
            } else if (!(lastChunk instanceof String && ((String) lastChunk).endsWith("[DONE]"))) {
                emitter.send(SseEmitter.event().data("data: [DONE]\n\n"));
            }

        } catch (Exception e) {
            log.error("调用工具时发生错误: ", e);
            try {
                Map<String, Object> errorResponse = Map.of(
                        "code", 500,
                        "message", "调用工具时出错",
                        "data", Map.of(
                                "content", e.getMessage(),
                                "think", ""
                        )
                );
                emitter.send(SseEmitter.event().data("data: " +
                        objectMapper.writeValueAsString(errorResponse) + "\n\n"));
                emitter.send(SseEmitter.event().data("data: [DONE]\n\n"));
            } catch (IOException ioException) {
                log.error("发送错误响应时发生异常: ", ioException);
            }
        } finally {
            emitter.complete();
        }
    }
    */

    private void sendErrorResponse(SseEmitter emitter, String message, int code) {
        try {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", code);
            errorResponse.put("message", message);

            Map<String, Object> data = new HashMap<>();
            data.put("content", message);
            data.put("think", "");

            errorResponse.put("data", data);

            emitter.send(SseEmitter.event().data(errorResponse));
            emitter.send(SseEmitter.event().data("[DONE]"));
            emitter.complete();
        } catch (IOException e) {
            emitter.completeWithError(e);
        }
    }

    // 请求智搜接口
//    private void intelligentResearchRequestApi(Map<String, Object> backgroundInfo, String chatId, String query,
//                                               String conversationId, SseEmitter sseEmitter) throws Exception {
//        // Step 1: 获取token
//        String token;
//        try {
//            token = getTokenByZhiSou(String.valueOf(backgroundInfo.get("bpcAuthorization")));
//        } catch (Exception e) {
//            log.error("获取智搜token时发生错误: ", e);
//            return;
//        }
//
//        // Step 2: 构造请求参数
//        Map<String, Object> requestBody = new HashMap<>();
//        requestBody.put("query", query);
//        requestBody.put("messageUuid", chatId);
//        requestBody.put("sessionUuid", conversationId);
//        requestBody.put("ifDeepThink", true);  // 是否开启深度思考
//        requestBody.put("origin", "联通小研");  // 来源
//        requestBody.put("backgroundInfo", backgroundInfo);
//
//        // Step 3: 发起流式请求
//        Request request = new Request.Builder()
//                .url(intelligentResearchUrl)
//                .header("Authorization", "Bearer " + token)
//                .post(RequestBody.create(
//                        objectMapper.writeValueAsString(requestBody),
//                        MediaType.parse("application/json")))
//                .build();
//
//        EventSources.createFactory(okHttpClient).newEventSource(request, new EventSourceListener() {
//            @Override
//            public void onEvent(@NotNull EventSource eventSource, String id, String type, @NotNull String data) {
//                try {
//                    List<Object> parsedData = parseChunk(data);
//                    if (parsedData instanceof Map<?, ?> mapData) {
//                        String answer = (String) mapData.get("answer");
//                        if (answer != null) {
//                            sseEmitter.send(answer);
//                        }
//                    } else {
//                        sseEmitter.send(parsedData.toString());
//                    }
//                } catch (Exception e) {
//                    sseEmitter.completeWithError(e);
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull EventSource eventSource, Throwable t, Response response) {
//                try {
//                    String errorMessage;
//                    if (t instanceof java.net.SocketTimeoutException) {
//                        errorMessage = "[错误] 读取超时（服务器响应太慢）";
//                    } else if (t instanceof java.net.ConnectException) {
//                        errorMessage = "[错误] 连接超时（无法连接到服务器）";
//                    } else if (response != null) {
//                        errorMessage = String.format("[错误] 请求失败，状态码: %d, 响应: %s",
//                                response.code(), response.body() != null ? response.body().string() : "");
//                    } else {
//                        errorMessage = String.format("[错误] 请求过程中发生异常: %s", t.getMessage());
//                    }
//                    sseEmitter.send(errorMessage);
//                    sseEmitter.completeWithError(new Exception(errorMessage));
//                } catch (Exception e) {
//                    sseEmitter.completeWithError(e);
//                }
//            }
//
//            @Override
//            public void onClosed(@NotNull EventSource eventSource) {
//                sseEmitter.complete();
//            }
//        });
//
//
//    }


    /**
     * 获取智搜token
     */
//    private String getTokenByZhiSou(String bpcAuthorization) {
//        BpcUser userInfo = httpClientService.getBpcCurrentUserInfo(bpcAuthorization);
//        Map<String, Object> authData = new HashMap<>();
//        authData.put("loginName", userInfo.getUserAccount());
//        authData.put("userName", userInfo.getUserName());
//        authData.put("email", userInfo.getEmail());
//        authData.put("phoneNumber", userInfo.getMobile());
//        authData.put("organizationCode", userInfo.getDeptID());
//        authData.put("organizationName", userInfo.getDeptName());
//        log.info("接口请求参数auth_data:{}", authData);
//        return httpClientService.getZhiSouToken(authData);
//    }

//    private StringBuilder buffer = new StringBuilder();

    /**
     * 解析数据块，返回完整的data内容列表
     * @param chunk 接收到的数据块
     * @return 解析后的数据列表（包含Map或String类型）
     */
//    public List<Object> parseChunk(String chunk) {
//        buffer.append(chunk);
//        List<Object> completeDataList = new ArrayList<>();
//
//        // 按SSE标准的双换行符分割事件
//        while (buffer.indexOf("\n\n") != -1) {
//            String[] parts = buffer.toString().split("\n\n", 2);
//            String eventBlock = parts[0];
//            buffer = new StringBuilder(parts.length > 1 ? parts[1] : "");
//
//            // 解析事件块中的data行
//            for (String line : eventBlock.split("\n")) {
//                line = line.trim();
//                if (line.startsWith("data:")) {
//                    String dataContent = line.substring(5).trim(); // 去掉'data: '前缀
//                    if (!dataContent.isEmpty()) {
//                        try {
//                            // 尝试解析JSON
//                            Map<String, Object> jsonData = objectMapper.readValue(dataContent, Map.class);
//                            completeDataList.add(jsonData);
//                        } catch (JsonProcessingException e) {
//                            // 如果不是有效JSON，直接添加原始内容
//                            completeDataList.add(dataContent);
//                        }
//                    }
//                }
//            }
//        }
//
//        return completeDataList;
//    }

}
