package com.chinaunicom.aiAgents.dao.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@DS("portal")
public interface PortalMapper {

    @Select("""
            SELECT
              up.pro_id as projectId,
              p.name as projectName
             FROM sys_user_pro_default up left join sys_project_new p
             on up.pro_id = p.id
             where up.user_id = #{userId}  limit 1
            """)
    List<Map<String, String>> queryProjectByUserId(@Param("userId") String userId);


    @Select("""
            <script>
            select 
                bucket_name,
                key_name,
                file_name
                
            from dm_file
                where  url is not null and key_name in 
                         <foreach item="item" index="index" collection="keyNames"
                                 open="(" separator="," close=")">
                            #{item}
                        </foreach>
            </script>
            """)
    List<Map<String, String>> queryDmFiles(@Param("keyNames") List<String> keyNames);


    @Select("""
            select 
                prompt_text
            from ai_prompt
                where llm_name = #{llmName} and tool_name = #{toolName} and agent_name = #{agentName} limit 1 
            """)
    List<Map<String, String>> queryPrompt(@Param("llmName") String llmName, @Param("toolName") String toolName, @Param("agentName") String agent_name);
}
