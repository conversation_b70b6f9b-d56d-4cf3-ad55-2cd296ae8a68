package com.chinaunicom.aiAgents.dao.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.chinaunicom.aiAgents.dao.domain.AiAgentConfig;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

public interface DmInfoMapper {


    @Select("""
                SELECT *
                FROM dm_info
                WHERE dm_number = #{dmNumber}
            """)
    Map queryDmInfo(@Param("dmNumber") String dmNumber);
}
