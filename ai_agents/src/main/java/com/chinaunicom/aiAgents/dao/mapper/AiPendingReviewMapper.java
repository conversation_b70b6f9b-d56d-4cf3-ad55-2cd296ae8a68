package com.chinaunicom.aiAgents.dao.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chinaunicom.aiAgents.dao.domain.AiPendingReview;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/// 涉及 ai_pending_review 何 ai_pending_review_user 两张数据表的相关sql操作
///     这两个表都是待办 要组合使用   就不拆开写了
@DS("portal")
public interface AiPendingReviewMapper extends BaseMapper<AiPendingReview> {

    //新增待办主体数据
    void addPending(AiPendingReview item);
    //新增待办负责人表数据
    void addPendingResponsibleUser(@Param("todoId") String todoId, @Param("userIds") List<String> userIds,
                                   @Param("stateShow") String stateShow, @Param("stateLogic") int stateLogic,
                                   @Param("buttonsJson") String buttonsJson);

    //根据pendingId获取pending信息
    AiPendingReview queryPendingById(@Param("Id") String pendingId);

    //根据pendId pendFun pendModel 查询待办
    AiPendingReview queryItemByPendIdAndPendFunAndPendModel(@Param("pendId") String pendId, @Param("pendFun") String pendFun, @Param("pendModel") String pendModel);


    //指定user 和待办id  查询数据  保护校验使用
    Map queryUserPending(@Param("userId") String userId, @Param("pendingId") String pendingId);

    //修改 指定某个待办 某个负责人的待办的状态, 单条操作
    void updateUserPendingState(@Param("pendingId") String pendingId, @Param("userId") String userId,
                                @Param("stateShow") String stateShow, @Param("stateLogic") int stateLogic);
    //更新待办主表的 状态
    void updatePendingState(@Param("pendingId") String pendingId,@Param("stateShow") String stateShow, @Param("stateLogic") int stateLogic);

    // 修改待办下的所有负责人的待办状态
    void updatePendingAllUserState(@Param("todoId") String todoId, @Param("stateShow") String stateShow, @Param("stateLogic") int stateLogic);

    // 根据todoId 查询待办下的负责人数量
    int  queryPendingUserCount(@Param("todoId") String todoId);
}
