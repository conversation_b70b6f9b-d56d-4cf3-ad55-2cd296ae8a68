//package com.chinaunicom.dataSync.cron.requirement;
//
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
//import com.chinaunicom.common.service.DocParseService;
//import com.chinaunicom.dataSync.config.SyncConfig;
//import com.chinaunicom.dataSync.dao.domain.DmFile;
//import com.chinaunicom.dataSync.dao.mapper.DmFileMapper;
//import com.chinaunicom.dataSync.dao.mapper.SyncDemandManageMapper;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.tuple.Pair;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.Resource;
//import java.util.Date;
//import java.util.List;
//import java.util.concurrent.LinkedBlockingQueue;
//import java.util.concurrent.TimeUnit;
//
//
//@Slf4j
//@Component
//public class DmFileSyncJob extends ThreadPoolTaskExecutor {
//
//
//    @Resource
//    DmFileMapper dmFileMapper;
//
//    @Resource
//    SyncDemandManageMapper syncDemandManageMapper;
//
//
//
//
//    LinkedBlockingQueue<String> syncDmFiles = new LinkedBlockingQueue<>();
//
//
//    @PostConstruct
//    private void init() {
//        int corePoolSize = 20; // 核心线程数
//        int maxPoolSize = 30; // 最大线程数
//        this.setDaemon(true);
//        this.setThreadNamePrefix("需求file同步-");
//        this.setMaxPoolSize(maxPoolSize);
//        this.setCorePoolSize(corePoolSize);
//        this.setQueueCapacity(100);
//        this.setKeepAliveSeconds(60);
//    }
//
//
//    public void syncDmFile(String keyName) {
//
//        try {
//
//            //查询dm_info表，dm_file表的新数据，is_sync_agent字段不为1
//            List<DmFile> dmFileList = dmFileMapper.selectList(new LambdaQueryWrapper<DmFile>()
//                    .eq(DmFile::getIsSyncAgent, 0)
//                    .eq(DmFile::getKeyName, keyName)
//                    .like(DmFile::getFileName, "%doc%")
//                    .orderByDesc(DmFile::getUploadTime)
//                    .last("limit 50"));
//            log.info("syncDmFile--dmFileList size {}", dmFileList.size());
//
//            for (DmFile dmFile : dmFileList) {
//                this.execute(() -> {
//                    try {
//                        Pair<String, String> res = docParseService.parseDoc(dmFile.getFileName(),
//                                dmFile.getKeyName());
//
//                        dmFile.setUrl(res.getLeft());
//                        dmFile.setFileContent(res.getRight());
//                        dmFile.setFetchContentTime(new Date());
//                        /*区分新版本的 增量同步*/
//                        dmFile.setStatus("3");
//
//                        int i = syncDemandManageMapper.insertDmFileForVector(dmFile);
//                        if (i > 0) {
//                            dmFileMapper.update(new LambdaUpdateWrapper<DmFile>()
//                                    .eq(DmFile::getId, dmFile.getId())
//                                    .set(DmFile::getIsSyncAgent, 1));
//                        }
//
//                    } catch (Exception e) {
//                        log.error("", e);
//                    }
//                });
//            }
//        } catch (Exception e) {
//            log.error("", e);
//        }
//
//    }
//}
