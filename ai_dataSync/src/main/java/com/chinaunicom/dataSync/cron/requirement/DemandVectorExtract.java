package com.chinaunicom.dataSync.cron.requirement;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinaunicom.common.embedding.EmbeddingService;
import com.chinaunicom.dataSync.config.SyncConfig;
import com.chinaunicom.dataSync.dao.domain.DmSearchVector;
import com.chinaunicom.dataSync.dao.mapper.DmSearchVectorMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;


@Slf4j
@Component
public class DemandVectorExtract extends ThreadPoolTaskExecutor {

    @Resource
    DmSearchVectorMapper dmSearchVectorMapper;

    @Resource
    EmbeddingService embeddingService;

    @Resource
    SyncConfig syncConfig;


    @PostConstruct
    private void init() {
        int corePoolSize = 10; // 核心线程数
        int maxPoolSize = 11; // 最大线程数
        this.setDaemon(true);
        this.setThreadNamePrefix("需求预处理-");
        this.setMaxPoolSize(maxPoolSize);
        this.setCorePoolSize(corePoolSize);
        this.setQueueCapacity(100);
        this.setKeepAliveSeconds(60);
    }


    @Scheduled(cron = "59 * * * * ?")
    public void embeddingGenerate() {
        if (syncConfig.getIsDmFeatureVectoredEnable() && this.getThreadPoolExecutor().getActiveCount() == 0) {
            List<DmSearchVector> dmSearchVectors = dmSearchVectorMapper.selectList(new LambdaQueryWrapper<DmSearchVector>()
                    .isNull(DmSearchVector::getMainVector)
                    .isNull(DmSearchVector::getSecondVector)
                    .isNotNull(DmSearchVector::getMainKeywords)
                    .isNotNull(DmSearchVector::getSecondKeywords)
                    .last("LIMIT 100"));
            for (DmSearchVector dmSearchVector : dmSearchVectors) {
                this.execute(() -> {
                    List<Double> mainVectors = embeddingService.getEmbedding(dmSearchVector.getMainKeywords());
                    List<Double> secondVectors = embeddingService.getEmbedding(dmSearchVector.getSecondKeywords());
                    // List<Double> vectors = embeddingService.getEmbedding(String.format("%s,%s", dmSearchVector.getSummary(), dmSearchVector.getKeyword()));

                    log.info("{} vectors {}, {}", dmSearchVector.getDemandNumber(), mainVectors.size(), secondVectors.size());
                    dmSearchVectorMapper.update(new LambdaUpdateWrapper<DmSearchVector>()
                            .eq(DmSearchVector::getId, dmSearchVector.getId())
                            .set(true, DmSearchVector::getMainVector, mainVectors.toString())
                            .set(true, DmSearchVector::getSecondVector, secondVectors.toString())
                    );
                });
            }
        } else {
            log.info("当前线程并发数 {}，无法添加新任务", this.getThreadPoolExecutor().getActiveCount());
        }
    }


}
