package com.chinaunicom.dataSync.cron.requirement;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinaunicom.common.configuration.ChatModelInfos;
import com.chinaunicom.common.embedding.EmbeddingService;
import com.chinaunicom.dataSync.config.SyncConfig;
import com.chinaunicom.dataSync.dao.domain.DmSearchVector;
import com.chinaunicom.dataSync.dao.mapper.DmSearchVectorMapper;
import com.chinaunicom.dataSync.dao.mapper.RagDemandMapper;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.output.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Component
public class DemandKeyWordExtract extends ThreadPoolTaskExecutor {

    private final ChatLanguageModel chatLanguageModel;

    @Resource
    RagDemandMapper ragDemandMapper;
    @Resource
    DmSearchVectorMapper dmSearchVectorMapper;

    @Resource
    SyncConfig syncConfig;

    DemandKeyWordExtract(@Autowired ChatModelInfos chatModelInfos) {
        chatLanguageModel = chatModelInfos.getChatLlm("deepseek-V3-tq-prod",
                1.0, false);
    }

    @PostConstruct
    private void init() {
        int corePoolSize = 3; // 核心线程数
        int maxPoolSize = 4; // 最大线程数
        this.setDaemon(true);
        this.setThreadNamePrefix("需求预处理-");
        this.setMaxPoolSize(maxPoolSize);
        this.setCorePoolSize(corePoolSize);
        this.setQueueCapacity(100);
        this.setKeepAliveSeconds(60);
    }


    @Scheduled(cron = "30 * * * * ?")
    public void requirementPreProcess() {
        // 检查当前队列是否为空
        if (syncConfig.getIsDmFeatureExtractEnable() && this.getThreadPoolExecutor().getActiveCount() == 0) {
            /*40个*/
            List<Map<String, String>> demands = ragDemandMapper.queryDemandInfo();
            if (demands.isEmpty()) {
                log.info("[需求特征提取]任务队列为空，跳过");
                return;
            }
            /*调用模型进行需求预处理*/
            ragDemandMapper.setDemandInfoVectored(demands.stream().map(v -> v.get("dm_number"))
                    .collect(Collectors.toList()));

            for (Map<String, String> demand : demands) {
                this.execute(() -> {
                    List<Map<String, String>> demandFiles = ragDemandMapper.queryDemandFiles(demand.get("process_instance_id"));
                    if (!demandFiles.isEmpty()) {
                        demandFiles.forEach(v -> {
                            if (v == null) {
                                return;
                            }
                            String fileContent = v.get("file_content");
                            log.info("{} 附件 长度 {}", demand.get("dm_number"), fileContent.length());
                            if (fileContent.length() > 10000) {
                                v.put("file_content", fileContent.substring(0, 5000));
                            }
                        });
                    }

                    try {
                        Response<AiMessage> resp = chatLanguageModel.generate(getChatMsgs(demand, demandFiles));
                        DmSearchVector dmSearchVector = getChatMsgs(demand, resp.content().text());
                        dmSearchVectorMapper.insert(dmSearchVector);

                    } catch (RuntimeException e) {
                        /*ToDo 异常捕捉待优化 */
                        DmSearchVector dmSearchVector = getChatMsgs(demand, e.getMessage());
                        dmSearchVectorMapper.insert(dmSearchVector);
                    }


                });
            }
        } else {
            log.info("[需求特征提取]任务队列不为空或未开启，跳过");
        }
    }


    /**
     * {
     * "名称":"",
     * "相关系统": ["",""],
     * "技术关键词": ["",""],
     * "业务关键词": ["",""],
     * "功能关键词": ["",""],
     * "需求相关方":  ["",""],
     * "总结": ""
     * }
     *
     * @param demand
     * @param resp
     * @return
     */
    private DmSearchVector getChatMsgs(Map<String, String> demand, String resp) {
        DmSearchVector dmSearchVector = new DmSearchVector();
        dmSearchVector.setDemandId(demand.get("id_"));
        dmSearchVector.setDemandNumber(demand.get("dm_number"));

        StringBuilder errReason = new StringBuilder();
        String ans = resp.replaceAll("```json", "")
                .replaceAll("```", "")
                .replaceAll("“", "\"")
                .trim();
        dmSearchVector.setAiSummaryOriginalDoc(ans);
        try {
            JsonObject json = JsonParser.parseString(ans).getAsJsonObject();
            String relatedSystems = json.getAsJsonArray("相关系统").asList()
                    .stream()
                    .map(JsonElement::getAsString).collect(Collectors.joining(","));
            String keywords1 = json.getAsJsonArray("技术关键词").asList()
                    .stream()
                    .map(JsonElement::getAsString).collect(Collectors.joining(","));
            String keywords2 = json.getAsJsonArray("业务关键词").asList()
                    .stream()
                    .map(JsonElement::getAsString).collect(Collectors.joining(","));
            String keywords3 = json.getAsJsonArray("功能关键词").asList()
                    .stream()
                    .map(JsonElement::getAsString).collect(Collectors.joining(","));
            String relatedDemanders = json.getAsJsonArray("需求相关方").asList()
                    .stream()
                    .map(JsonElement::getAsString).collect(Collectors.joining(","));
//            String summary = json.get("总结").getAsString();
            dmSearchVector.setMainKeywords(relatedSystems + ";;" + relatedDemanders);
            dmSearchVector.setSecondKeywords(keywords1 + ";;" + keywords2 + ";;" + keywords3);
        } catch (JsonSyntaxException ex) {
            log.error("JSON解析错误: {}", ex.getMessage());
            errReason.append(ex.getMessage()).append("\n");

        }

        try {
            JsonObject json = JsonParser.parseString(ans).getAsJsonObject();
            String summary = json.get("总结").getAsString();
            dmSearchVector.setSummary(summary);
        } catch (JsonSyntaxException ex) {
            log.error("JSON解析错误: {}", ex.getMessage());
            errReason.append(ex.getMessage());
        }
        dmSearchVector.setErrReason(errReason.toString());
        return dmSearchVector;
    }

//    public static void main(String[] args) {
//        JsonObject json = JsonParser.parseString("""
//
//                {
//                  "类型": ["系统功能开发"],
//                  "功能关键词": ["权益查询", "套餐变更", "流量查询", "5G计费", "上网记录"],
//                  "总结": "实现[会员权益查询、套餐变更、流量查询等核心功能]，支持[国联通APP用户]在[自助服务场景]下的操作，满足[用户对资源余量、权益详情、套餐变更等关键需求]，保障[系统稳定与数据安全]，适配[移动通信服务系统]"
//                }
//
//                """).getAsJsonObject();
//        String type = json.getAsJsonArray("类型").asList()
//                .stream()
//                .map(JsonElement::getAsString).collect(Collectors.joining(","));
//        String keywords = json.getAsJsonArray("功能关键词").asList()
//                .stream()
//                .map(JsonElement::getAsString).collect(Collectors.joining(","));
//        String summary = json.get("总结").getAsString();
//        System.out.println(type);
//        System.out.println(keywords);
//        System.out.println(summary);
//    }

    private List<ChatMessage> getChatMsgs(Map<String, String> demand, List<Map<String, String>> demandFiles) {
        List<ChatMessage> chatMessages = new ArrayList<>();

        String prompt = """
                请依据用户输入的需求内容，精准提取需求类型、需求功能总结关键词，并进行总结。输出需严格遵循 JSON 格式，
                必须包含以下字段：
                - [名称]：[数据类型，string]，表述应言简意赅，字数控制在 10 个字以内。
                - [相关系统]：[数据类型，array]，指承接需求，或者需求影响的相关 IT 系统。
                - [技术关键词]：[数据类型，array]，编程语言、框架、工具、协议等（如Java/Kubernetes/OAuth2.0），不超过 5 个。
                - [业务关键词]：[数据类型，array]，领域术语、用户角色、核心流程（如电商/风控/支付对账），不超过 5 个。
                - [功能关键词]：[数据类型，array]，系统模块、操作动作（如用户认证/日志导出/API限流），不超过 5 个。
                - [需求相关方]：[数据类型，array]，业务方、技术团队、用户、合规部门、管理层等利益相关者，不超过 3个。
                - [总结]：[数据类型，string]，不要包含双引号，按照以下格式输出，为实现[目标]，需在[时间]前完成[功能/模块]的[开发/优化/修复]，优先级为[高/中/低]，涉及[相关系统/团队]，关键依赖[条件]。           
                                
                返回的JSON必须符合以下结构示例：：
                { 
                   "名称":"",
                    "相关系统": ["",""], 
                    "技术关键词": ["",""], 
                    "业务关键词": ["",""], 
                    "功能关键词": ["",""], 
                    "需求相关方":  ["",""], 
                    "总结": ""
                }

                请确保：
                1. 使用双引号包裹所有键和字符串值
                2. 不使用任何注释或解释性文字
                3. 严格遵循JSON语法规范，确保输出的JSON能被标准JSON解析器正确解析
                4. 所有字符串值中的双引号(")必须使用反斜杠转义为\"
                """;
        chatMessages.add(SystemMessage.systemMessage(prompt));
        String wordDesc = """
                dm_requester_dept comment : 需求提出部门
                dm_name comment : 需求名称
                dm_desc comment : 需求描述
                dm_requester_dept comment : 需求提出部门
                dm_requester_source comment : 需求提出来源
                dm_target_type_name comment : 需求目标类型名称
                dm_type_name comment : 需求业务类型名称
                dm_type_value comment : 需求提出类型名称
                file_content comment : 需求文件详细描述内容
                """;
        chatMessages.add(SystemMessage.systemMessage(wordDesc));

        demand.remove("process_instance_id");
        chatMessages.add(UserMessage.from(demand.toString()));
        chatMessages.add(UserMessage.from(demandFiles.toString()));


        return chatMessages;
    }
}
