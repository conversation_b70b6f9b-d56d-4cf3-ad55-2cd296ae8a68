package com.chinaunicom.dataSync.cron.requirement;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinaunicom.common.utils.LogUtils;
import com.chinaunicom.dataSync.config.SyncConfig;
import com.chinaunicom.dataSync.dao.domain.DmFile;
import com.chinaunicom.dataSync.dao.domain.DmInfo;
import com.chinaunicom.dataSync.dao.mapper.*;
import com.chinaunicom.common.service.DocParseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Component
public class DmInfoSyncJob extends ThreadPoolTaskExecutor {


    @Resource
    DmInfoMapper dmInfoMapper;

    @Resource
    DmFileMapper dmFileMapper;

    @Resource
    DocParseService docParseService;

    @Resource
    SyncDemandManageMapper syncDemandManageMapper;


    @Resource
    SyncConfig syncConfig;


    @PostConstruct
    private void init() {
        int corePoolSize = 55; // 核心线程数
        int maxPoolSize = 60; // 最大线程数
        this.setDaemon(true);
        this.setThreadNamePrefix("需求info和 file同步-");
        this.setMaxPoolSize(maxPoolSize);
        this.setCorePoolSize(corePoolSize);
        this.setQueueCapacity(100);
        this.setKeepAliveSeconds(60);
    }

    private void syncDmFile(String keyName) {

        try {
            //查询dm_info表，dm_file表的新数据，is_sync_agent字段不为1
            List<DmFile> dmFileList = dmFileMapper.selectList(new LambdaQueryWrapper<DmFile>()
                    .eq(DmFile::getIsSyncAgent, 0)
                    .eq(DmFile::getKeyName, keyName)
                    // .like(DmFile::getFileName, "%doc%")
                    .orderByDesc(DmFile::getUploadTime)
                    .last("limit 50"));

            for (DmFile dmFile : dmFileList) {
                this.execute(() -> {
                    try {
                        Pair<String, String> res = docParseService.parseDoc(dmFile.getFileName(),
                                dmFile.getKeyName());

                        dmFile.setUrl(res.getLeft());
                        // 截取 res.getRight() 的最大长度为 10000
                        String content = res.getRight();
                        if (content != null && content.length() > 10000) {
                            content = content.substring(0, 10000);
                        }
                        dmFile.setFileContent(content);
                        dmFile.setFetchContentTime(new Date());
                        /*区分新版本的 增量同步*/
                        dmFile.setStatus("3");

                        int i = syncDemandManageMapper.insertDmFileForVector(dmFile);
                        if (i > 0) {
                            dmFileMapper.update(new LambdaUpdateWrapper<DmFile>()
                                    .eq(DmFile::getId, dmFile.getId())
                                    .set(DmFile::getIsSyncAgent, 1));
                        }

                    } catch (Exception e) {
                        log.error("", e);
                    }
                });
            }
        } catch (Exception e) {
            log.error("", e);
        }

    }

    @Scheduled(cron = "0/10 * * * * ?")
    public void syncDmInfo() {

        if (syncConfig.getIsDmInfoSyncEnable() && this.getThreadPoolExecutor().getActiveCount() == 0) {
            try {
                //查询dm_info表，dm_file表的新数据，is_sync_agent字段不为1
                //同步已经结束流程的需求
                //is_sync_agent = 0 and dm_create_time &lt; '2025-06-15 00:00:00'
                List<DmInfo> dmInfoList = dmInfoMapper.selectList(new LambdaQueryWrapper<DmInfo>()
                        .eq(DmInfo::getIsSyncAgent, 0)
//                        .isNotNull(DmInfo::getDmEndTime)
//                        .eq(DmInfo::getDmStatus, "完成")
                        .ne(DmInfo::getDmStatus, "撤销")
                        .ne(DmInfo::getDmStatus, "测试")
                        .ne(DmInfo::getDmStatus, "UAT")
//                        .le(DmInfo::getDmCreateTime, "2025-06-15 00:00:00")
                        .orderByDesc(DmInfo::getDmEndTime)
                        .last("limit 1"));
                if (!ObjectUtils.isEmpty(dmInfoList)) {
                    for (DmInfo dmInfo : dmInfoList) {
                        this.execute(() -> {
                            //插入数据
                            try {
                                Date date = new Date();
                                dmInfo.setUpTime(date);
                                dmInfo.setDeleted(0);

                                /*查询相关附件并同步rag库的 dm_file*/
                                List<DmFile> dmFiles = dmFileMapper.selectList(new LambdaQueryWrapper<DmFile>()
                                        .eq(DmFile::getProcessInstanceId, dmInfo.getProcessInstanceId()));

                                log.info("{} 需求存在附件{}个，{} ", dmInfo.getDmNumber(), dmFiles.size(),
                                        dmFiles.stream().map(DmFile::getFileName).collect(Collectors.joining(",")));
                                dmFiles.forEach(v -> syncDmFile(v.getKeyName()));

                                /*同步rag库的dm_info*/
                                int flg = syncDemandManageMapper.insertDmInfoForVector(dmInfo);

                                if (flg > 0) {
                                    dmInfoMapper.update(new LambdaUpdateWrapper<DmInfo>()
                                            .eq(DmInfo::getId, dmInfo.getId())
                                            .set(DmInfo::getIsSyncAgent, 1));
                                    log.info("dmInfo表数据同步成功，dmNumber:{}", dmInfo.getDmNumber());
                                }
                                //   LOGGER.info(methodName, dmInfo.getDmNumber() + " : syncDmInfo--successEnd");
                            } catch (Exception e) {
                                log.error("", e);
                            }
                        });
                    }
                }
            } catch (Exception e) {
                log.error("", e);
            }
        } else {
            log.info("syncDmInfo--当前队列大小 {} 个", this.getThreadPoolExecutor().getQueue().size());
        }

    }


}
