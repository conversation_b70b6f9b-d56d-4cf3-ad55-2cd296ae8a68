package com.chinaunicom.dataSync.controller;


//import com.aio.portable.swiss.suite.log.facade.LogHub;
//import com.aio.portable.swiss.suite.log.factory.LogHubFactory;
//import com.aio.portable.swiss.suite.log.solution.elk.ESLogRecordItem;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinaunicom.common.entity.uniRequest.CommonAgentRequest;
import com.chinaunicom.dataSync.config.SyncConfig;
import com.chinaunicom.dataSync.dao.domain.DmInfo;
import com.chinaunicom.dataSync.dao.mapper.DmInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 同步需求管理数据
 */
@RestController
@RequestMapping("/syncDemandData")
@Slf4j
public class SyncDemandDataController {

    @Resource
    SyncConfig syncConfig;

    @Resource
    DmInfoMapper dmInfoMapper;

    @PostMapping(value = "/config")
    public String aiChat(@RequestBody SyncConfig req) {
        log.info("配置请求 {}", req);

        syncConfig.setIsDmFileSyncEnable(req.getIsDmFileSyncEnable());
        syncConfig.setIsDmInfoSyncEnable(req.getIsDmInfoSyncEnable());
        syncConfig.setIsDmFeatureVectoredEnable(req.getIsDmFeatureVectoredEnable());
        syncConfig.setIsDmFeatureExtractEnable(req.getIsDmFeatureExtractEnable());

        log.info("配置更新 {}", syncConfig);

        return "success";
    }

    @GetMapping(value = "/test")
    public Object test() {
        List<DmInfo> dmInfoList = dmInfoMapper.selectList(new LambdaQueryWrapper<DmInfo>()
                .eq(DmInfo::getIsSyncAgent, 0)
                .le(DmInfo::getDmCreateTime, "2025-06-15 00:00:00")
                .orderByDesc(DmInfo::getDmCreateTime)
                .last("limit 10"));

        return dmInfoList;
    }


}
