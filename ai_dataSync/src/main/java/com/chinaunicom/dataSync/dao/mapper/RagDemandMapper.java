package com.chinaunicom.dataSync.dao.mapper;


import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;


public interface RagDemandMapper {

    /**
     * df.file_summary
     *
     * @return
     */
    @Select("""
            select 
                   di.id_,
                   di.dm_number,
                   di.dm_requester_dept,
                   di.dm_name,
                   di.dm_desc,
                   di.dm_requester_dept,
                   di.dm_requester_source,
                   di.dm_target_type_name,
                   di.dm_type_name,
                   di.fill_requester_dept,
                   di.dm_type_value,
                   di.process_instance_id
            from dm_info di where is_vectored = 0
            limit 40
            """)
    List<Map<String, String>> queryDemandInfo();


    @Update("""
            <script>
            update dm_info 
                set is_vectored = 1
            where is_vectored = 0 and dm_number in
                        <foreach item="item" index="index" collection="dmNumbers"
                                 open="(" separator="," close=")">
                            #{item}
                        </foreach>
            </script>
            """)
    int setDemandInfoVectored(@Param("dmNumbers") List<String> dmNumbers);

    @Select("""
            select file_content
            from dm_file where process_instance_id = #{processInstanceId}
            """)
    List<Map<String, String>> queryDemandFiles(@Param("processInstanceId") String processInstanceId);
}
