package com.chinaunicom.dataSync.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @TableName dm_search_vector
 */
@TableName(value = "dm_search_vector")
@Data
public class DmSearchVector {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 需求id
     */
    private String demandId;

    private String demandNumber;

    private String mainKeywords;
    /**
     * 关键词
     */
    private String secondKeywords;

    private String summary;
    /**
     * 原始文档
     */
    private String aiSummaryOriginalDoc;
    /**
     *
     */
    private String mainVector;

    private String secondVector;

    private String summaryVector;

    private String errReason;
}