package com.chinaunicom.dataSync.dao.mapper;

import com.chinaunicom.dataSync.dao.domain.DmFile;
import com.chinaunicom.dataSync.dao.domain.DmInfo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;


public interface SyncDemandManageMapper {


    @Insert("""
            INSERT INTO dm_info (
                       id_,
                        back_status,
                        current_handler,
                        current_handler_name,
                        current_task_name,
                        data_dm_type,
                        data_dm_type_name,
                        dm_create_time,
                        dm_desc,
                        dm_end_time,
                        dm_estimate_cost,
                        dm_estimate_cost_name,
                        dm_name,
                        dm_number,
                        dm_requester,
                        dm_requester_dept,
                        dm_requester_name,
                        dm_requester_source,
                        dm_requester_sub_source,
                        dm_source,
                        dm_status,
                        dm_target_type,
                        dm_target_type_name,
                        dm_type,
                        dm_type_name,
                        fill_requester,
                        fill_requester_dept,
                        fill_requester_name,
                        fill_requester_source,
                        fill_requester_sub_source,
                        is_data_dm,
                        is_other_propose,
                        is_pre_assess_work,
                        is_uat_test,
                        plan_online_time,
                        process_instance_id,
                        professional_line,
                        professional_line_name,
                        remote_process_id,
                        remote_process_key,
                        sensitive_data,
                        sensitive_data_name,
                        unsupported_reason,
                        unsupported_reason_name,
                        dept_id,
                        dm_type_code,
                        dm_type_value,
                        up_time,
                        low_code_result,
                        support_scene,
                        anti_fraud_demand,
                        deleted
            )  
            VALUES (
                   #{dmInfo.id},
                   #{dmInfo.backStatus},
                   #{dmInfo.currentHandler},
                   #{dmInfo.currentHandlerName},
                   #{dmInfo.currentTaskName},
                   #{dmInfo.dataDmType},
                   #{dmInfo.dataDmTypeName},
                   #{dmInfo.dmCreateTime},
                   #{dmInfo.dmDesc},
                   #{dmInfo.dmEndTime},
                   #{dmInfo.dmEstimateCost},
                   #{dmInfo.dmEstimateCostName},
                   #{dmInfo.dmName},
                   #{dmInfo.dmNumber},
                   #{dmInfo.dmRequester},
                   #{dmInfo.dmRequesterDept},
                   #{dmInfo.dmRequesterName},
                   #{dmInfo.dmRequesterSource},
                   #{dmInfo.dmRequesterSubSource},
                   #{dmInfo.dmSource},
                   #{dmInfo.dmStatus},
                   #{dmInfo.dmTargetType},
                   #{dmInfo.dmTargetTypeName},
                   #{dmInfo.dmType},
                   #{dmInfo.dmTypeName},
                   #{dmInfo.fillRequester},
                   #{dmInfo.fillRequesterDept},
                   #{dmInfo.fillRequesterName},
                   #{dmInfo.fillRequesterSource},
                   #{dmInfo.fillRequesterSubSource},
                   #{dmInfo.isDataDm},
                   #{dmInfo.isOtherPropose},
                   #{dmInfo.isPreAssessWork},
                   #{dmInfo.isUatTest},
                   #{dmInfo.planOnlineTime},
                   #{dmInfo.processInstanceId},
                   #{dmInfo.professionalLine},
                   #{dmInfo.professionalLineName},
                   #{dmInfo.remoteProcessId},
                   #{dmInfo.remoteProcessKey},
                   #{dmInfo.sensitiveData},
                   #{dmInfo.sensitiveDataName},
                   #{dmInfo.unsupportedReason},
                   #{dmInfo.unsupportedReasonName},
                   #{dmInfo.deptId},
                   #{dmInfo.dmTypeCode},
                   #{dmInfo.dmTypeValue},
                   #{dmInfo.upTime},
                   #{dmInfo.lowCodeResult},
                   #{dmInfo.supportScene},
                   #{dmInfo.antiFraudDemand},
                   #{dmInfo.deleted}
            )           
            """)
    int insertDmInfoForVector(@Param("dmInfo") DmInfo dmInfo);


    @Insert("""
                INSERT INTO dm_file (
                    id_                    ,
                    bucket_name            ,
                    dm_process_instance_id ,
                    file_id                ,
                    file_name              ,
                    file_system            ,
                    file_type              ,
                    key_name               ,
                    process_instance_id    ,
                    upload_link           ,
                    upload_time            ,
                    uploader_              ,
                    uploader_dept          ,
                    uploader_name          ,
                    url                    ,
                    valid                  ,
                    version                ,
                    file_content           ,
                    fetch_content_time     ,
                    status 
                ) VALUES (
                    #{dmFile.id},
                    #{dmFile.bucketName},
                    #{dmFile.dmProcessInstanceId},
                    #{dmFile.fileId},
                    #{dmFile.fileName},
                    #{dmFile.fileSystem},
                    #{dmFile.fileType},
                    #{dmFile.keyName},
                    #{dmFile.processInstanceId},
                    #{dmFile.uploadLink},
                    #{dmFile.uploadTime},
                    #{dmFile.uploader},
                    #{dmFile.uploaderDept},
                    #{dmFile.uploaderName},
                    #{dmFile.url},
                    #{dmFile.valid},
                    #{dmFile.version},
                    #{dmFile.fileContent},
                    #{dmFile.fetchContentTime},
                    #{dmFile.status}
                )
            """)
    int insertDmFileForVector(@Param("dmFile") DmFile dmFile);
}
