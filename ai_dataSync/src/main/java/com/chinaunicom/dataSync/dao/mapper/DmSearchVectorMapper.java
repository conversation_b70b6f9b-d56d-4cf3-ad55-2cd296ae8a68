package com.chinaunicom.dataSync.dao.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.chinaunicom.dataSync.dao.domain.DmSearchVector;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【dm_search_vector】的数据库操作Mapper
 * @createDate 2025-07-01 13:09:04
 * @Entity generator.domain.DmSearchVector
 */
public interface DmSearchVectorMapper extends BaseMapper<DmSearchVector> {

    @Select("""
            select 
                 demand_number,
                 summary,
                 main_keywords,
                 second_keywords,
                 ai_summary_original_doc,
                 main_dis* #{mainWeight}+second_dis*#{secondWeight} as total_dis 
            from (             
                 SELECT 
                 demand_number,
                 summary,
                 main_keywords,
                 second_keywords,
                 ai_summary_original_doc,
                 Inner_product(main_vector, #{mainVector}) as main_dis,
                 Inner_product(second_vector, #{secondVector}) as second_dis
                 FROM dm_search_vector 
            ) a
            order by total_dis desc limit 30
            """)
    List<Map<String, Object>> queryByVector(@Param("mainVector") String mainVector,
                                            @Param("secondVector") String secondVector,
                                            @Param("mainWeight") Double mainWeight,
                                            @Param("secondWeight") Double secondWeight);

}




