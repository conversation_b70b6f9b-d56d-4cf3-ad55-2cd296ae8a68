<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds"
               debug="false">
    <contextName>agent_base</contextName>
    <springProperty scope="context" name="kafka_topic"
                    source="spring.log.kafka.template.defaultTopic" defaultValue="application-log"/>
    <springProperty scope="context" name="kafka_bootstrap_servers"
                    source="spring.log.kafka.producer.bootstrap-servers"
                    defaultValue="10.172.82.191:32048,10.172.129.8:32037,10.172.129.7:32032"/>
    <springProperty scope="context" name="kafka_sasl_jaas_config"
                    source="spring.log.kafka.properties.sasl.jaas.config"
                    defaultValue="org.apache.kafka.common.security.scram.ScramLoginModule required username=&quot;tianti-log-kafka&quot; password=&quot;BNS8sn2jS8&quot;"/>
    <!-- 从 Spring 配置中获取 es.index 属性的值 -->
    <springProperty scope="context" name="es.index"
                    source="spring.log.kafka.es-index" defaultValue="agent_base-uat-xx-skyladder-log"/>



    <springProperty scope="context" name="app.name"
                    source="spring.application.name" defaultValue="agent_base"/>
    <springProperty scope="context" name="log.path"
                    source="spring.log.path" defaultValue="./logs"/>
    <property name="normal.pattern"
              value="%d{yyyy-MM-dd HH:mm:ss:SSS} [%thread] %-5level %logger{36}[%L] [%X{ctxTraceId}]- %msg%n"/>

    <!-- 配置 AbstractWordConverter 类的日志等级为 DEBUG -->
    <logger name="org.apache.poi" level="ERROR"/>
    <!--    <logger name="org.apache.kafka.clients.producer" level="ERROR"/>-->

    <!--输出到控制台 -->
    <appender name="console"
              class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <encoder>
            <pattern>${normal.pattern}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <appender name="kafkaAppender"
              class="com.github.danielwegener.logback.kafka.KafkaAppender">
        <encoder charset="UTF-8"
                 class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <mdc/>
                <pattern>
                    <pattern>
                        {
                        "esIndex": "${es.index}",
                        "level": "%level",
                        "requestId": "%X{requestId}",
                        "threadName": "%thread",
                        "name": "%logger{40}",
                        "message": "%message",
                        "timestamp": "%d{yyyy-MM-dd'T'HH:mm:ss.SSSZ}",
                        "lineno": "%L",
                        "exception": "%exception{10}",
                        "host": "*************"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>

        <topic>${kafka_topic}</topic>
        <!--        <topic>application-log</topic>-->
        <keyingStrategy
                class="com.github.danielwegener.logback.kafka.keying.HostNameKeyingStrategy"/>
        <deliveryStrategy
                class="com.github.danielwegener.logback.kafka.delivery.AsynchronousDeliveryStrategy"/>
        <!--        <producerConfig>bootstrap.servers=${spring.log.kafka.producer.bootstrap-servers}</producerConfig>-->
        <producerConfig>bootstrap.servers=${kafka_bootstrap_servers}</producerConfig>
        <!-- don't wait for a broker to ack the reception of a batch. -->
        <!--        <producerConfig>acks=${spring.log.kafka.properties.acks}</producerConfig>-->
        <producerConfig>acks=1</producerConfig>
        <!-- wait up to 1000ms and collect log messages before sending them as
            a batch -->
        <producerConfig>linger.ms=100</producerConfig>
        <producerConfig>compression.type=gzip</producerConfig>
        <!-- even if the producer buffer runs full, do not block the application
            but start to drop messages -->
        <!--<producerConfig>max.block.ms=0</producerConfig> -->
        <producerConfig>security.protocol=SASL_PLAINTEXT</producerConfig>
        <producerConfig>sasl.mechanism=SCRAM-SHA-256</producerConfig>
        <producerConfig>sasl.jaas.config=${kafka_sasl_jaas_config}</producerConfig>
        <producerConfig>block.on.buffer.full=false</producerConfig>
        <producerConfig>request.timeout.ms=1000</producerConfig>
        <producerConfig>transaction.timeout.ms=1000</producerConfig>
        <producerConfig>sasl.username=tianti-log</producerConfig>
        <producerConfig>sasl.password=BNS8sn2jS8</producerConfig>
        <!-- kafka连接失败后，使用下面配置进行日志输出 -->
        <appender-ref ref="console"/>
    </appender>

    <!--输出到文件 info -->
    <appender name="file"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy
                class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名 -->
            <FileNamePattern>${log.path}/${app.name}.%d{yyyyMMdd}.%i.log
            </FileNamePattern>
            <!--日志文件最大的大小 -->
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!--日志文件保留天数 -->
            <MaxHistory>30</MaxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${normal.pattern}</pattern>
        </encoder>
    </appender>

    <!-- 输出到文件 error -->
    <appender name="file-error"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
        <rollingPolicy
                class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名 -->
            <FileNamePattern>${log.path}/${app.name}-error.%d{yyyyMMdd}.%i.log
            </FileNamePattern>
            <!--日志文件最大的大小 -->
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!--日志文件保留天数 -->
            <MaxHistory>30</MaxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <layout>
            <pattern>${normal.pattern}</pattern>
        </layout>
    </appender>

    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <neverBlock>true</neverBlock>
        <appender-ref ref="kafkaAppender"/>
    </appender>


    <root level="info">
        <!--        <appender-ref ref="console"/>-->
        <appender-ref ref="file"/>
        <!-- <appender-ref ref="file-error" />-->


        <appender-ref ref="ASYNC"/>
    </root>

</configuration>
