<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chinaunicom.dataSync.dao.mapper.SyncDemandManageMapper">

    <!-- 基础字段映射 -->
    <resultMap id="BaseResultMap" type="com.chinaunicom.dataSync.dao.domain.DmInfo">
        <id property="id" column="id_" jdbcType="VARCHAR"/>
        <result property="backStatus" column="back_status" jdbcType="VARCHAR"/>
        <result property="currentHandler" column="current_handler" jdbcType="VARCHAR"/>
        <result property="currentHandlerName" column="current_handler_name" jdbcType="VARCHAR"/>
        <result property="currentTaskName" column="current_task_name" jdbcType="VARCHAR"/>
        <result property="dataDmType" column="data_dm_type" jdbcType="VARCHAR"/>
        <result property="dataDmTypeName" column="data_dm_type_name" jdbcType="VARCHAR"/>
        <result property="dmCreateTime" column="dm_create_time" jdbcType="TIMESTAMP"/>
        <result property="dmDesc" column="dm_desc" jdbcType="VARCHAR"/>
        <result property="dmEndTime" column="dm_end_time" jdbcType="TIMESTAMP"/>
        <result property="dmEstimateCost" column="dm_estimate_cost" jdbcType="VARCHAR"/>
        <result property="dmEstimateCostName" column="dm_estimate_cost_name" jdbcType="VARCHAR"/>
        <result property="dmName" column="dm_name" jdbcType="VARCHAR"/>
        <result property="dmNumber" column="dm_number" jdbcType="VARCHAR"/>
        <result property="dmRequester" column="dm_requester" jdbcType="VARCHAR"/>
        <result property="dmRequesterDept" column="dm_requester_dept" jdbcType="VARCHAR"/>
        <result property="dmRequesterName" column="dm_requester_name" jdbcType="VARCHAR"/>
        <result property="dmRequesterSource" column="dm_requester_source" jdbcType="VARCHAR"/>
        <result property="dmRequesterSubSource" column="dm_requester_sub_source" jdbcType="VARCHAR"/>
        <result property="dmSource" column="dm_source" jdbcType="VARCHAR"/>
        <result property="dmStatus" column="dm_status" jdbcType="VARCHAR"/>
        <result property="dmTargetType" column="dm_target_type" jdbcType="VARCHAR"/>
        <result property="dmTargetTypeName" column="dm_target_type_name" jdbcType="VARCHAR"/>
        <result property="dmType" column="dm_type" jdbcType="VARCHAR"/>
        <result property="dmTypeName" column="dm_type_name" jdbcType="VARCHAR"/>
        <result property="fillRequester" column="fill_requester" jdbcType="VARCHAR"/>
        <result property="fillRequesterDept" column="fill_requester_dept" jdbcType="VARCHAR"/>
        <result property="fillRequesterName" column="fill_requester_name" jdbcType="VARCHAR"/>
        <result property="fillRequesterSource" column="fill_requester_source" jdbcType="VARCHAR"/>
        <result property="fillRequesterSubSource" column="fill_requester_sub_source" jdbcType="VARCHAR"/>
        <result property="isDataDm" column="is_data_dm" jdbcType="INTEGER"/>
        <result property="isOtherPropose" column="is_other_propose" jdbcType="INTEGER"/>
        <result property="isPreAssessWork" column="is_pre_assess_work" jdbcType="INTEGER"/>
        <result property="isUatTest" column="is_uat_test" jdbcType="INTEGER"/>
        <result property="planOnlineTime" column="plan_online_time" jdbcType="DATE"/>
        <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
        <result property="professionalLine" column="professional_line" jdbcType="VARCHAR"/>
        <result property="professionalLineName" column="professional_line_name" jdbcType="VARCHAR"/>
        <result property="remoteProcessId" column="remote_process_id" jdbcType="VARCHAR"/>
        <result property="remoteProcessKey" column="remote_process_key" jdbcType="VARCHAR"/>
        <result property="sensitiveData" column="sensitive_data" jdbcType="VARCHAR"/>
        <result property="sensitiveDataName" column="sensitive_data_name" jdbcType="VARCHAR"/>
        <result property="unsupportedReason" column="unsupported_reason" jdbcType="VARCHAR"/>
        <result property="unsupportedReasonName" column="unsupported_reason_name" jdbcType="VARCHAR"/>
        <result property="deptId" column="dept_id" jdbcType="VARCHAR"/>
        <result property="dmTypeCode" column="dm_type_code" jdbcType="VARCHAR"/>
        <result property="dmTypeValue" column="dm_type_value" jdbcType="VARCHAR"/>
        <result property="upTime" column="up_time" jdbcType="TIMESTAMP"/>
        <result property="lowCodeResult" column="low_code_result" jdbcType="VARCHAR"/>
        <result property="supportScene" column="support_scene" jdbcType="VARCHAR"/>
        <result property="antiFraudDemand" column="anti_fraud_demand" jdbcType="INTEGER"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
        <result property="dmAllSummary" column="dm_all_summary" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 字段列表复用片段 -->
    <sql id="Base_Column_List">
        id_, back_status, current_handler, current_handler_name,
        current_task_name, data_dm_type, data_dm_type_name,
        dm_create_time, dm_desc, dm_end_time, dm_estimate_cost,
        dm_estimate_cost_name, dm_name, dm_number, dm_requester,
        dm_requester_dept, dm_requester_name, dm_requester_source,
        dm_requester_sub_source, dm_source, dm_status, dm_target_type,
        dm_target_type_name, dm_type, dm_type_name, fill_requester,
        fill_requester_dept, fill_requester_name, fill_requester_source,
        fill_requester_sub_source, is_data_dm, is_other_propose,
        is_pre_assess_work, is_uat_test, plan_online_time,
        process_instance_id, professional_line, professional_line_name,
        remote_process_id, remote_process_key, sensitive_data,
        sensitive_data_name, unsupported_reason, unsupported_reason_name,
        dept_id, dm_type_code, dm_type_value, up_time, low_code_result,
        support_scene, anti_fraud_demand, deleted, dm_all_summary
    </sql>

    <!--    <insert id="insertDmInfoForVector" parameterType="com.chinaunicom.dataSync.dao.domain.DmInfo">-->
    <!--        INSERT INTO dm_info (-->
    <!--            id_,-->
    <!--            back_status,-->
    <!--            current_handler,-->
    <!--            current_handler_name,-->
    <!--            current_task_name,-->
    <!--            data_dm_type,-->
    <!--            data_dm_type_name,-->
    <!--            dm_create_time,-->
    <!--            dm_desc,-->
    <!--            dm_end_time,-->
    <!--            dm_estimate_cost,-->
    <!--            dm_estimate_cost_name,-->
    <!--            dm_name,-->
    <!--            dm_number,-->
    <!--            dm_requester,-->
    <!--            dm_requester_dept,-->
    <!--            dm_requester_name,-->
    <!--            dm_requester_source,-->
    <!--            dm_requester_sub_source,-->
    <!--            dm_source,-->
    <!--            dm_status,-->
    <!--            dm_target_type,-->
    <!--            dm_target_type_name,-->
    <!--            dm_type,-->
    <!--            dm_type_name,-->
    <!--            fill_requester,-->
    <!--            fill_requester_dept,-->
    <!--            fill_requester_name,-->
    <!--            fill_requester_source,-->
    <!--            fill_requester_sub_source,-->
    <!--            is_data_dm,-->
    <!--            is_other_propose,-->
    <!--            is_pre_assess_work,-->
    <!--            is_uat_test,-->
    <!--            plan_online_time,-->
    <!--            process_instance_id,-->
    <!--            professional_line,-->
    <!--            professional_line_name,-->
    <!--            remote_process_id,-->
    <!--            remote_process_key,-->
    <!--            sensitive_data,-->
    <!--            sensitive_data_name,-->
    <!--            unsupported_reason,-->
    <!--            unsupported_reason_name,-->
    <!--            dept_id,-->
    <!--            dm_type_code,-->
    <!--            dm_type_value,-->
    <!--            up_time,-->
    <!--            low_code_result,-->
    <!--            support_scene,-->
    <!--            anti_fraud_demand,-->
    <!--        deleted-->
    <!--        )-->
    <!--        VALUES (-->
    <!--                   #{id},-->
    <!--                   #{backStatus},-->
    <!--                   #{currentHandler},-->
    <!--                   #{currentHandlerName},-->
    <!--                   #{currentTaskName},-->
    <!--                   #{dataDmType},-->
    <!--                   #{dataDmTypeName},-->
    <!--                   #{dmCreateTime},-->
    <!--                   #{dmDesc},-->
    <!--                   #{dmEndTime},-->
    <!--                   #{dmEstimateCost},-->
    <!--                   #{dmEstimateCostName},-->
    <!--                   #{dmName},-->
    <!--                   #{dmNumber},-->
    <!--                   #{dmRequester},-->
    <!--                   #{dmRequesterDept},-->
    <!--                   #{dmRequesterName},-->
    <!--                   #{dmRequesterSource},-->
    <!--                   #{dmRequesterSubSource},-->
    <!--                   #{dmSource},-->
    <!--                   #{dmStatus},-->
    <!--                   #{dmTargetType},-->
    <!--                   #{dmTargetTypeName},-->
    <!--                   #{dmType},-->
    <!--                   #{dmTypeName},-->
    <!--                   #{fillRequester},-->
    <!--                   #{fillRequesterDept},-->
    <!--                   #{fillRequesterName},-->
    <!--                   #{fillRequesterSource},-->
    <!--                   #{fillRequesterSubSource},-->
    <!--                   #{isDataDm},-->
    <!--                   #{isOtherPropose},-->
    <!--                   #{isPreAssessWork},-->
    <!--                   #{isUatTest},-->
    <!--                   #{planOnlineTime},-->
    <!--                   #{processInstanceId},-->
    <!--                   #{professionalLine},-->
    <!--                   #{professionalLineName},-->
    <!--                   #{remoteProcessId},-->
    <!--                   #{remoteProcessKey},-->
    <!--                   #{sensitiveData},-->
    <!--                   #{sensitiveDataName},-->
    <!--                   #{unsupportedReason},-->
    <!--                   #{unsupportedReasonName},-->
    <!--                   #{deptId},-->
    <!--                   #{dmTypeCode},-->
    <!--                   #{dmTypeValue},-->
    <!--                   #{upTime},-->
    <!--                   #{lowCodeResult},-->
    <!--                   #{supportScene},-->
    <!--                   #{antiFraudDemand},-->
    <!--        #{deleted}-->
    <!--               )-->
    <!--    </insert>-->


</mapper>