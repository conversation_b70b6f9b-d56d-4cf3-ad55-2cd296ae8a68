<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.6</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.chinaunicom</groupId>
    <artifactId>agent_base</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>agent-base</name>
    <description>xiaoyan all agents service</description>
    <modules>
        <module>ai_common</module>
        <module>ai_agents</module>
        <module>ai_dataSync</module>
    </modules>
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <mysql-connector-java.version>8.0.22</mysql-connector-java.version>

        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <mybatis-plus-boot-starter.version>3.5.11</mybatis-plus-boot-starter.version>
        <mybatis-spring.version>3.0.4</mybatis-spring.version>

        <dev.langchain4j.version>0.36.2</dev.langchain4j.version>
        <dynamic-datasource-spring-boot-starter.version>4.3.0</dynamic-datasource-spring-boot-starter.version>
        <oceanbase-jdbc.version>2.4.2</oceanbase-jdbc.version>
        <apache-poi.version>5.4.1</apache-poi.version>
        <apollo.version>2.4.0</apollo.version>

        <logback.version>1.4.14</logback.version>
        <liteflow.version>2.13.0</liteflow.version>
        <lombok.version>1.18.32</lombok.version>

        <guava.version>31.0.1-jre</guava.version>

        <redisson.version>3.22.0</redisson.version>
    </properties>


    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <optional>true</optional>
                <!--必须该版本，否则日志 ELK 不生效-->
                <version>2.2.7.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>2.0.1</version>
            </dependency>

            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-spring-boot-starter</artifactId>
                <version>${dev.langchain4j.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/dev.langchain4j/langchain4j-ollama-spring-boot-starter -->
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-ollama-spring-boot-starter</artifactId>
                <version>${dev.langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-open-ai</artifactId>
                <version>${dev.langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j</artifactId>
                <version>${dev.langchain4j.version}</version>
            </dependency>

            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-milvus</artifactId>
                <version>${dev.langchain4j.version}</version>
            </dependency>

            <!--            <dependency>-->
            <!--                <groupId>dev.langchain4j</groupId>-->
            <!--                <artifactId>langchain4j-document-parser-apache-poi</artifactId>-->
            <!--                <version>${dev.langchain4j.version}</version>-->
            <!--            </dependency>-->

            <!-- 基础依赖 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huaban</groupId>
                <artifactId>jieba-analysis</artifactId>
                <version>1.0.2</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>3.0.5</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic-datasource-spring-boot-starter.version}</version>
            </dependency>


            <!-- junrar 用于解压 RAR 文件 -->
            <dependency>
                <groupId>com.github.junrar</groupId>
                <artifactId>junrar</artifactId>
                <version>7.4.1</version>
            </dependency>

            <dependency>
                <groupId>com.oceanbase</groupId>
                <artifactId>oceanbase-client</artifactId>
                <version>${oceanbase-jdbc.version}</version>
            </dependency>


            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3</artifactId>
                <version>2.20.0</version>
            </dependency>

            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>5.0</version>
            </dependency>
            <dependency>
                <groupId>com.github.danielwegener</groupId>
                <artifactId>logback-kafka-appender</artifactId>
                <version>0.2.0-RC1</version>
                <scope>runtime</scope>
            </dependency>


            <!-- Apollo配置 -->
            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apollo.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.12.0</version> <!-- 请检查最新版本 -->
            </dependency>


            <!-- Guava 缓存依赖 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version> <!-- 请使用最新的稳定版本 -->
            </dependency>


            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <!--            <version>${logback.version}</version>-->
            </dependency>

            <!--            <dependency>-->
            <!--                <groupId>org.slf4j</groupId>-->
            <!--                <artifactId>slf4j-api</artifactId>-->
            <!--                &lt;!&ndash;            <version>2.12.1</version>&ndash;&gt;-->
            <!--            </dependency>-->

            <dependency>
                <groupId>net.sf.json-lib</groupId>
                <artifactId>json-lib</artifactId>
                <version>2.4</version>
                <classifier>jdk17</classifier>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${apache-poi.version}</version>
            </dependency>
            <!-- Apache POI OOXML 依赖 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${apache-poi.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.poi/poi-scratchpad -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>${apache-poi.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>liteflow-spring-boot-starter</artifactId>
                <version>${liteflow.version}</version>
            </dependency>


            <!-- https://mvnrepository.com/artifact/com.alibaba.fastjson2/fastjson2 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>2.0.57</version>
            </dependency>


            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>2.24.3</version>
            </dependency>

            <!-- 如果需要使用 javax.annotation (Java EE)，需要手动添加依赖 -->
            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>1.3.2</version>
            </dependency>


        </dependencies>

    </dependencyManagement>


    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>


        <!-- Apache POI 核心依赖 -->


        <!--        &lt;!&ndash; Apache POI OOXML 模式依赖 &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.poi</groupId>-->
        <!--            <artifactId>poi-ooxml-schemas</artifactId>-->
        <!--            <version>${apache-poi.version}</version>-->
        <!--        </dependency>-->
        <!--        &lt;!&ndash; XMLBeans 依赖 &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.xmlbeans</groupId>-->
        <!--            <artifactId>xmlbeans</artifactId>-->
        <!--            <version>${apache-poi.version}</version>-->
        <!--        </dependency>-->


        <!--        <dependency>-->
        <!--            <groupId>org.codehaus.janino</groupId>-->
        <!--            <artifactId>janino</artifactId>-->
        <!--            <version>3.1.6</version>  &lt;!&ndash; 使用最新版本 &ndash;&gt;-->
        <!--        </dependency>-->


        <!--        &lt;!&ndash; https://mvnrepository.com/artifact/org.apache.httpcomponents.client5/httpclient5 &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.httpcomponents.client5</groupId>-->
        <!--            <artifactId>httpclient5</artifactId>-->
        <!--            <version>5.2.1</version>-->
        <!--        </dependency>-->


        <!--  解析 token 依赖  -->
        <!--        <dependency>-->
        <!--            <groupId>org.jasig.cas.client</groupId>-->
        <!--            <artifactId>cas-client-core</artifactId>-->
        <!--            <version>3.6.4</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.auth0</groupId>-->
        <!--            <artifactId>java-jwt</artifactId>-->
        <!--            <version>3.4.0</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.jasig.cas.client</groupId>-->
        <!--            <artifactId>cas-client-core</artifactId>-->
        <!--            <version>${cas-client-core.version}</version>-->
        <!--        </dependency>-->


    </dependencies>



    <repositories>
        <repository>
            <id>nexus-snapshots</id>
            <url>http://10.124.128.3:8081/nexus/content/groups/public</url>
            <snapshots>
                <enabled>true</enabled>
                <checksumPolicy>fail</checksumPolicy>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>ladder_public</id>
            <url>http://10.124.128.3:8081/nexus/content/groups/Tianti_Public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
