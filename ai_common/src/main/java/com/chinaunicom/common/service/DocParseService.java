package com.chinaunicom.common.service;

import com.github.junrar.Archive;
import com.github.junrar.rarfile.FileHeader;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.net.ssl.*;
import java.io.*;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

import static org.apache.commons.io.FileUtils.deleteDirectory;

@Slf4j
@Service
public class DocParseService {

    /*ToDo 配置化 */
    @Value("${dmfile.bucket:prod-tianti-img}")
    String dmFileBucket;

    @Value("${dmfile.url:https://cos.xx-pbc.cos.tg.unicom.local/188206434527}")
    String dmFileUrl;


    /**
     * 解析 DOCX 文件并返回文件内容
     * ToDo: txt，PDF，xls 格式的文件处理支持
     *
     * @return 文件内容的字符串表示
     * @throws IOException 读取文件时发生错误
     */
    public Pair<String, String> parseDoc(String fileName, String keyName) throws IOException {
        String finalFilePath = String.format("%s:%s/%s^1^%s", dmFileUrl, dmFileBucket, keyName, fileName);

        log.info("download url {}", finalFilePath);

        StringBuilder content = new StringBuilder();
        try (InputStream inputStream = downloadFileWithHttpsBypass(finalFilePath);) {
            if (finalFilePath.toLowerCase().endsWith(".doc")) {
                // 处理 .doc 文件
                HWPFDocument document = new HWPFDocument(inputStream);

                WordExtractor extractor = new WordExtractor(document);

                return new ImmutablePair<>(finalFilePath, extractor.getText());
            } else if (finalFilePath.toLowerCase().endsWith(".docx")) {
                // 处理 .docx 文件
                XWPFDocument document = new XWPFDocument(inputStream);
                for (XWPFParagraph paragraph : document.getParagraphs()) {
                    content.append(paragraph.getText()).append("\n");
                }
                return new ImmutablePair<>(finalFilePath, content.toString());
            } else if (finalFilePath.toLowerCase().endsWith(".pdf")) {
                // 处理 .pdf 文件
                try (PDDocument pdfDocument = Loader.loadPDF(inputStream.readAllBytes())) {
                    PDFTextStripper stripper = new PDFTextStripper();
                    String pdfText = stripper.getText(pdfDocument);
                    return new ImmutablePair<>(finalFilePath, pdfText);
                } catch (Exception e) {
                    log.error("{} 解析失败 {}", keyName, finalFilePath);
                    throw new IOException("Failed to parse PDF file");
                }
            } else if (finalFilePath.toLowerCase().endsWith(".txt")) {
                // 处理 .txt 文件
                try (BufferedReader br = new BufferedReader(new java.io.InputStreamReader(inputStream))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        content.append(line).append("\n");
                    }
                    return new ImmutablePair<>(finalFilePath, content.toString());
                }
            } else if (finalFilePath.toLowerCase().endsWith(".xls")) {
                // 处理 .xls 文件
                try (Workbook workbook = new HSSFWorkbook(inputStream)) {
                    parseExcel(workbook, content);
                    return new ImmutablePair<>(finalFilePath, content.toString());
                }

            } else if (finalFilePath.toLowerCase().endsWith(".xlsx")) {
                // 处理 .xlsx 文件
                try (Workbook workbook = new XSSFWorkbook(inputStream)) {
                    parseExcel(workbook, content);
                    return new ImmutablePair<>(finalFilePath, content.toString());
                }
            } else if (finalFilePath.toLowerCase().endsWith(".rar")) {
                // 处理 rar. 文件

                Path tempDir = Files.createTempDirectory("rar_extract");
                try {
                    File tempRarFile = File.createTempFile("temp_rar", ".rar");
                    try (FileOutputStream fos = new FileOutputStream(tempRarFile)) {
                        inputStream.transferTo(fos);
                    }
                    extractRar(tempRarFile, tempDir.toFile());
                    content.append(readExtractedFiles(tempDir));
                } finally {
                    deleteDirectory(tempDir.toFile());
                }
                return new ImmutablePair<>(finalFilePath, content.toString());
            } else if (finalFilePath.toLowerCase().endsWith(".zip")) {
                // 处理 zip 文件
                Path tempDir = Files.createTempDirectory("zip_extract");
                try {
                    File tempZipFile = File.createTempFile("temp_zip", ".zip");
                    try (FileOutputStream fos = new FileOutputStream(tempZipFile)) {
                        inputStream.transferTo(fos);
                    }
                    extractZip(tempZipFile, tempDir.toFile());
                    content.append(readExtractedFiles(tempDir));
                } finally {
                    deleteDirectory(tempDir.toFile());
                }
                return new ImmutablePair<>(finalFilePath, content.toString());
            } else {
                log.error("{} 暂不支持的文件 {}", keyName, finalFilePath);
                throw new IOException("Unsupported file format");
            }
        }

    }

    /**
     * 读取解压后的文件内容
     *
     * @param directory 解压后的目录
     * @return 文件内容
     * @throws IOException 读取文件时发生错误
     */
    private String readExtractedFiles(Path directory) throws IOException {
        StringBuilder content = new StringBuilder();
        try (var fileStream = Files.walk(directory)) {
            fileStream.filter(Files::isRegularFile)
                    .forEach(file -> {
                        try {
                            String fileContent = Files.readString(file);
                            content.append("File: ").append(file.getFileName()).append("\n");
                            content.append(fileContent).append("\n\n");
                        } catch (IOException e) {
                            log.error("Failed to read file {}", file, e);
                        }
                    });
            return content.toString();
        }
    }

    /**
     * 解压 RAR 文件
     *
     * @param rarFile       RAR 文件
     * @param destDirectory 解压目标目录
     * @throws IOException 解压过程中发生错误
     */
    private void extractRar(File rarFile, File destDirectory) throws IOException {
        try (Archive archive = new Archive(rarFile)) {
            FileHeader fileHeader;
            while ((fileHeader = archive.nextFileHeader()) != null) {
                File outputFile = new File(destDirectory, fileHeader.getFileNameString().trim());
                if (fileHeader.isDirectory()) {
                    outputFile.mkdirs();
                } else {
                    outputFile.getParentFile().mkdirs();
                    try (BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(outputFile))) {
                        archive.extractFile(fileHeader, bos);
                    }
                }
            }
        } catch (Exception e) {
            throw new IOException("Failed to extract RAR file", e);
        }
    }


    /**
     * 解压 ZIP 文件
     *
     * @param zipFile       ZIP 文件
     * @param destDirectory 解压目标目录
     * @throws IOException 解压过程中发生错误
     */
    private void extractZip(File zipFile, File destDirectory) throws IOException {
        try (ZipFile zip = new ZipFile(zipFile)) {
            Enumeration<? extends ZipEntry> entries = zip.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                File outputFile = new File(destDirectory, entry.getName());
                if (entry.isDirectory()) {
                    outputFile.mkdirs();
                } else {
                    outputFile.getParentFile().mkdirs();
                    try (InputStream in = zip.getInputStream(entry);
                         BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(outputFile))) {
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = in.read(buffer)) > 0) {
                            bos.write(buffer, 0, len);
                        }
                    }
                }
            }
        }
    }

    /**
     * 删除目录及其所有内容
     *
     * @param directory 要删除的目录
     */
    private void deleteDirectory(File directory) {
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    deleteDirectory(file);
                } else {
                    file.delete();
                }
            }
        }
        directory.delete();
    }

    private void parseExcel(Workbook workbook, StringBuilder content) {
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            content.append("工作表名称: ").append(sheet.getSheetName()).append("\n");
            for (Row row : sheet) {
                for (Cell cell : row) {
                    CellType cellType = cell.getCellType();
                    switch (cellType) {
                        case STRING:
                            content.append(cell.getStringCellValue()).append("\t");
                            break;
                        case NUMERIC:
                            if (DateUtil.isCellDateFormatted(cell)) {
                                content.append(cell.getDateCellValue()).append("\t");
                            } else {
                                content.append(cell.getNumericCellValue()).append("\t");
                            }
                            break;
                        case BOOLEAN:
                            content.append(cell.getBooleanCellValue()).append("\t");
                            break;
                        case FORMULA:
                            content.append(cell.getCellFormula()).append("\t");
                            break;
                        default:
                            content.append("\t");
                    }
                }
                content.append("\n");
            }
        }
    }

    /**
     * 下载 URL 文件并实现 HTTPS 免校验
     *
     * @param fileUrl 文件的 URL
     * @return 文件的输入流
     * @throws IOException 下载文件时发生错误
     */
    private InputStream downloadFileWithHttpsBypass(String fileUrl) throws IOException {
        try {
            // 创建一个信任所有证书的 TrustManager
            TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        public X509Certificate[] getAcceptedIssuers() {
                            return null;
                        }

                        public void checkClientTrusted(X509Certificate[] certs, String authType) {
                        }

                        public void checkServerTrusted(X509Certificate[] certs, String authType) {
                        }
                    }
            };

            // 创建 SSLContext 并初始化
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());

            // 创建一个不验证主机名的 HostnameVerifier
            HostnameVerifier allHostsValid = (hostname, session) -> true;

            // 设置 HttpsURLConnection 使用自定义的 SSLSocketFactory 和 HostnameVerifier
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            // 打开连接
            URL url = new URL(fileUrl);
            HttpsURLConnection conn = (HttpsURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            return conn.getInputStream();
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            throw new IOException("Failed to initialize SSL context", e);
        }

    }


    public static void main(String[] args) throws IOException {
        DocParseService docParseService = new DocParseService();
        docParseService.parseDoc("广西壮族自治区隆安县人民法院调查取证函（卜柳婉）.pdf", "********************************");
    }

}
