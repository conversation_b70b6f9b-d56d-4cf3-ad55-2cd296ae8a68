package com.chinaunicom.common.configuration;


import com.chinaunicom.common.models.ModelInfo;
import com.chinaunicom.common.models.tianqAiModel.TQChatModel;
import com.chinaunicom.common.models.tianqAiModel.TQStreamingChatModel;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.chat.listener.ChatModelListener;
import dev.langchain4j.model.chat.listener.ChatModelRequestContext;
import dev.langchain4j.model.ollama.OllamaChatModel;
import dev.langchain4j.model.ollama.OllamaStreamingChatModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.openai.OpenAiStreamingChatModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.List;
import java.util.Map;

@Data
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "models")
public class ChatModelInfos {

    public final static String DEEPSEEK_R1_14B = "deepseek-r1-14b";

    public final static String DEEPSEEK_R1_7B = "deepseek-r1-7b";

    public final static String QWEN_7B = "qwen-7b";

    public final static String BGE_M3_LATEST = "bge-m3-latest";


    public final static String DEEPSEEK_R1 = "deepseek-r1";

    public final static String DEEPSEEK_V3 = "deepseek-V3";

    @Getter
    @AllArgsConstructor
    public enum MODEL_API_TYPE {
        OLLAMA("ollama"),
        OPENAPI("openapi"),
        TQ("tq");

        private String name;
    }

    private Map<String, ModelInfo> modelInfoMap;

    public StreamingChatLanguageModel getStreamingLlm(String modelName) {
        return getStreamingLlm(modelName, 0.7, true);
    }

    public StreamingChatLanguageModel getStreamingLlm(String modelName, Double temperature, boolean logRequests) {

        ModelInfo modelInfo = modelInfoMap.get(modelName);
        ChatModelListener listener = new ChatModelListener() {
            @Override
            public void onRequest(ChatModelRequestContext requestContext) {
                log.info("[{}] 请求内容 {},tool: {}", modelInfo.getModel(), requestContext.request().messages(),
                        requestContext.request().toolSpecifications());
            }
        };
        if (modelInfo.getType().contentEquals(MODEL_API_TYPE.OLLAMA.getName())) {
            return OllamaStreamingChatModel
                    .builder()
                    .baseUrl(modelInfo.getEndpoint())
                    .modelName(modelInfo.getModel())
                    .listeners(List.of(listener))
                    .timeout(Duration.ofSeconds(120))
                    .build();
        } else if (modelInfo.getType().contentEquals(MODEL_API_TYPE.OPENAPI.name)) {
            return OpenAiStreamingChatModel
                    .builder()
                    .apiKey(modelInfo.getApiKey())
                    .baseUrl(modelInfo.getEndpoint())
                    .modelName(modelInfo.getModel())
                    .listeners(List.of(listener))
                    .timeout(Duration.ofSeconds(120))
                    .build();
        } else if (modelInfo.getType().contentEquals(MODEL_API_TYPE.TQ.getName())) {
            return TQStreamingChatModel.builder()
                    .baseUrl(modelInfo.getEndpoint())
                    .modelName(modelInfo.getModel())
                    .apiName(modelInfo.getApiName())
                    .bodyModelName(modelInfo.getBodyModelName())
                    .timeout(Duration.ofSeconds(120))
                    .listeners(List.of(listener))
                    .apiKey(modelInfo.getApiKey())
                    .apisecret(modelInfo.getApiSecret())
                    .authorization(modelInfo.getAuthorization())
                    .temperature(modelInfo.getTemperature())
                    .logRequests(logRequests)
                    .build();
        } else {
            throw new RuntimeException("不支持的模型类型");
        }

    }


    public ChatLanguageModel getChatLlm(String modelName) {
        return getChatLlm(modelName, 0.7, true);
    }


    /* 获取聊天模型 目前没有做成 Bean */
    public ChatLanguageModel getChatLlm(String modelName, Double temperature, boolean logRequests) {

        ModelInfo modelInfo = modelInfoMap.get(modelName);

        modelInfo.setTemperature(temperature);
        ChatModelListener listener = new ChatModelListener() {
            @Override
            public void onRequest(ChatModelRequestContext requestContext) {
                log.info("[{}] 请求内容 {},tool: {}", modelInfo.getModel(),
                        requestContext.request().messages(),
                        requestContext.request().toolSpecifications());
            }
        };
        if (modelInfo.getType().contentEquals(MODEL_API_TYPE.OLLAMA.getName())) {
            return OllamaChatModel
                    .builder()
                    .baseUrl(modelInfo.getEndpoint())
                    .modelName(modelInfo.getModel())
                    .timeout(Duration.ofSeconds(120))
                    .listeners(List.of(listener))
                    .build();
        } else if (modelInfo.getType().contentEquals(MODEL_API_TYPE.OPENAPI.getName())) {
            return OpenAiChatModel
                    .builder()
                    .apiKey(modelInfo.getApiKey())
                    .baseUrl(modelInfo.getEndpoint())
                    .modelName(modelInfo.getModel())
                    .timeout(Duration.ofSeconds(120))
                    .listeners(List.of(listener))
                    .build();
        } else if (modelInfo.getType().contentEquals(MODEL_API_TYPE.TQ.getName())) {
            return TQChatModel.builder()
                    .baseUrl(modelInfo.getEndpoint())
                    .modelName(modelInfo.getModel())
                    .apiName(modelInfo.getApiName())
                    .bodyModelName(modelInfo.getBodyModelName())
                    .timeout(Duration.ofSeconds(120))
                    .apiKey(modelInfo.getApiKey())
                    .apisecret(modelInfo.getApiSecret())
                    .listeners(List.of(listener))
                    .authorization(modelInfo.getAuthorization())
                    .temperature(modelInfo.getTemperature())
                    .logRequests(logRequests)
                    .build();
        } else {
            throw new RuntimeException("不支持的模型类型");
        }
    }


}
