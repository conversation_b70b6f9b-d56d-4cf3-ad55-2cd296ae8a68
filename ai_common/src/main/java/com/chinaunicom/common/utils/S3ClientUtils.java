package com.chinaunicom.common.utils;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import javax.annotation.PostConstruct;
import java.io.*;
import java.nio.file.Path;


@Slf4j
@Component
public class S3ClientUtils {


    @Value("${aws.s3.accessKeyId}")
    private String accessKeyId;

    @Value("${aws.s3.secretAccessKey}")
    private String secretKey;

    @Value("${aws.s3.region}")
    private String region;

    @Value("${aws.s3.endpointUrl}")
    private String endpoint;

    @Value("${aws.s3.xiaoyanBucketName}")
    private String bucketName;

    private S3Client s3Client;

    @PostConstruct
    private void initializeAmazon() {
        AwsBasicCredentials awsCreds = AwsBasicCredentials.create(accessKeyId, secretKey);
        this.s3Client = S3Client.builder()
                .region(Region.of(region))
                .credentialsProvider(StaticCredentialsProvider.create(awsCreds))
                .endpointOverride(java.net.URI.create(endpoint))
                .build();
    }

    public String uploadFile(MultipartFile multipartFile, String uploader) throws IOException {
        File file = convertMultiPartToFile(multipartFile);
        String fileName = generateFileName(multipartFile);
        String key = uploader + "/" + fileName;
        PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(key)
                .build();
        Path filePath = file.toPath();
        s3Client.putObject(putObjectRequest, filePath);
        if (!file.delete()) {
            log.error("File deleted successfully: " + file.getName());
        }
        ;
        return key;
    }


    // todo: downloadFile还有一些问题
    public void downloadFile(String key, OutputStream outputStream) {
        InputStream inputStream = null;
        try {
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            inputStream = s3Client.getObject(getObjectRequest);

            byte[] readBuf = new byte[1024];
            int readLen;
            while ((readLen = inputStream.read(readBuf)) > 0) {
                outputStream.write(readBuf, 0, readLen);
            }
            outputStream.flush();

        } catch (Exception e) {
            log.error("downloadFile", e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.error("downloadFile", e);
            }

            try {
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                log.error("downloadFile", e);
            }

        }
    }

    public void listFilesWithPrefix(String prefix) {
        try {
            ListObjectsV2Request listReq = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(prefix)
                    .build();
            ListObjectsV2Response listRes = s3Client.listObjectsV2(listReq);
            for (S3Object content : listRes.contents()) {
                log.info(content.key());
            }
        } catch (Exception e) {
            log.error("listFilesWithPrefix", e);
        }
    }

    public String deleteFile(String fileName) {
        DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder()
                .bucket(bucketName)
                .key(fileName)
                .build();
        s3Client.deleteObject(deleteObjectRequest);
        return "File deleted successfully: " + fileName;
    }

    // Helper methods for converting and generating file names
    private File convertMultiPartToFile(MultipartFile file) throws IOException {
        File convFile = new File(file.getOriginalFilename());
        FileOutputStream fos = new FileOutputStream(convFile);
        fos.write(file.getBytes());
        fos.close();
        return convFile;
    }

    private String generateFileName(MultipartFile multiPart) {
        return System.currentTimeMillis() + "-" + multiPart.getOriginalFilename().replace(" ", "_");
    }
}