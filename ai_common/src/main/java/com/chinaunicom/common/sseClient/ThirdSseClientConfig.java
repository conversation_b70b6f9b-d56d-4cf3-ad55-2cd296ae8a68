package com.chinaunicom.common.sseClient;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okio.Buffer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import javax.net.ssl.*;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.time.Duration;

import static com.fasterxml.jackson.databind.SerializationFeature.INDENT_OUTPUT;

@Slf4j
@Configuration
public class ThirdSseClientConfig {

    @Value("${text2sql.url.prefix:http://10.168.32.90:31258}")
    private String text2sqlBaseUrl;

    @Value("${rag.url.prefix:http://10.168.32.90:31258}")
    private String fileRagBaseUrl;


    @Value("${tianq.prod.url:http://10.238.57.34:8000}")
    private String tqUrl;

    @Value("${zhisou.url:https://10.238.57.69:80}")
    private String zhisouUrl;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper().enable(INDENT_OUTPUT);

    // 配置 Spring 线程池 Bean
//    @Bean("okhttpThreadPool")
//    public ExecutorService customThreadPool() {
//        // 创建线程池
//        return new ThreadPoolExecutor(
//                5, // 核心线程数
//                10, // 最大线程数
//                60, // 线程空闲时间(秒)
//                TimeUnit.SECONDS,
//                new LinkedBlockingQueue<>(50), // 任务队列
//                new ThreadFactory() {
//                    private final AtomicInteger counter = new AtomicInteger(1);
//
//                    @Override
//                    public Thread newThread(Runnable r) {
//                        return new Thread(r, "okhttp-dispatcher-" + counter.getAndIncrement());
//                    }
//                },
//                new ThreadPoolExecutor.AbortPolicy() // 拒绝策略
//        );
//    }

    // 创建信任所有证书的 TrustManager
    private static TrustManager[] createTrustAllCerts() {
        return new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public void checkServerTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[]{};
                    }
                }
        };
    }

    @Bean
    public OkHttpClient SseClient() {
        Duration timeout = Duration.ofSeconds(10000);

        OkHttpClient.Builder okHttpClientBuilder = new OkHttpClient.Builder()
                .callTimeout(timeout)
                .connectTimeout(timeout)
                .readTimeout(timeout)
                .writeTimeout(timeout);

        try {
            // 创建信任所有证书的 TrustManager
            TrustManager[] trustAllCerts = createTrustAllCerts();
            // 获取 SSLContext 实例
            SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
            // 获取 SSLSocketFactory 实例
            SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

            // 配置 OkHttpClient 跳过证书校验
            okHttpClientBuilder.sslSocketFactory(sslSocketFactory, (X509TrustManager) trustAllCerts[0]);
            okHttpClientBuilder.hostnameVerifier((hostname, session) -> true);
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            log.error("配置 SSL 上下文失败", e);
        }
        okHttpClientBuilder.addInterceptor(new Interceptor() {

            @Override
            public Response intercept(Chain chain) throws IOException {
                Request request = chain.request();

                // 打印请求信息
                log.info("请求方法: {}, 请求 URL: {}, 请求头: {}", request.method(), request.url(), request.headers());

                if (request.body() != null) {
                    // 创建一个 Buffer 来存储请求体内容
                    Buffer buffer = new Buffer();
                    request.body().writeTo(buffer);
                    // 获取请求体的字符串内容
                    String requestBody = buffer.readUtf8();
                    log.info("请求体: {}", requestBody);
                }

                return chain.proceed(request);
            }
        });

        return okHttpClientBuilder.build();
    }


    /*demo测试使用*/
    @Bean
    public DemoDeepSeekApi demoDeepSeekApi(@Autowired OkHttpClient client) {

        // 配置 Retrofit
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl("https://api.deepseek.com/") // 替换为实际的 API 基础 URL
                .client(client)
                .addConverterFactory(JacksonConverterFactory.create(OBJECT_MAPPER))
                .build();

        // 创建 DemoDeepSeekApi 实例
        return retrofit.create(DemoDeepSeekApi.class);
    }

    @Bean
    public RagApi ragApi(@Autowired OkHttpClient client) {

        // 配置 Retrofit
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(text2sqlBaseUrl) // 替换为实际的 API 基础 URL
                .client(client)
                .addConverterFactory(JacksonConverterFactory.create(OBJECT_MAPPER))
                .build();

        // 创建 DemoDeepSeekApi 实例
        return retrofit.create(RagApi.class);
    }

    @Bean
    public DmGyApi dmGyApi(@Autowired OkHttpClient client) {

        // 配置 Retrofit
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(tqUrl) // 替换为实际的 API 基础 URL
                .client(client)
                .addConverterFactory(JacksonConverterFactory.create(new ObjectMapper()
                        .enable(INDENT_OUTPUT)
                        .setSerializationInclusion(JsonInclude.Include.NON_NULL)))
                .build();

        // 创建 DemoDeepSeekApi 实例
        return retrofit.create(DmGyApi.class);
    }

    @Bean
    public ZhiSouApi zhiSouApi(@Autowired OkHttpClient client) {
        // 配置 Retrofit
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(zhisouUrl)
                .client(client)
                .addConverterFactory(JacksonConverterFactory.create(OBJECT_MAPPER))
                .build();

        // 创建 DemoDeepSeekApi 实例
        return retrofit.create(ZhiSouApi.class);
    }

    public static EventSourceListener buildEventSourceListener(SseHandler handler) {
        return new EventSourceListener() {
            @Override
            public void onOpen(EventSource eventSource, okhttp3.Response response) {
                log.info("onOpen()");
            }

            @Override
            public void onEvent(EventSource eventSource, String id, String type, String dataString) {
                handler.onNext(dataString);
            }

            @Override
            public void onFailure(EventSource eventSource, Throwable t, okhttp3.Response response) {
                log.error("{}", response, t);
                handler.onError(t);
            }

            @Override
            public void onClosed(EventSource eventSource) {
                handler.onComplete();
            }

        };
    }


}
