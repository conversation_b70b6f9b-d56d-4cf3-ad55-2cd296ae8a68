package com.chinaunicom.common.models;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ChatRespType {

    /*默认值，前端不处理*/
    NORMAL("NORMAL"),

    /*立项助手思维链，按照 ANALYSIS -> CARD -> CONTENT*/
    ANALYSIS("ANALYSIS"),
    CARD("CARD"),
    CONTENT("CONTENT"),


    /*效能助手画图使用*/
    graphUsed("graphUsed"),

    /*无用*/
    error("error"),
    ;

    String progressType;
}
