package com.chinaunicom.common.models.tianqAiModel;


import com.chinaunicom.common.utils.TqApiHelper;
import com.fasterxml.jackson.databind.ObjectMapper;
import dev.ai4j.openai4j.chat.ChatCompletionChoice;
import dev.ai4j.openai4j.chat.ChatCompletionResponse;
import dev.ai4j.openai4j.chat.Delta;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.internal.Utils;
import dev.langchain4j.model.StreamingResponseHandler;
import dev.langchain4j.model.openai.OpenAiStreamingResponseBuilder;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import okio.Buffer;
import retrofit2.Call;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.io.IOException;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static com.fasterxml.jackson.databind.SerializationFeature.INDENT_OUTPUT;

@Slf4j
@Builder
public class TQClient {


    private final String baseUrl;
    private final TQApi tqApi;
    private final boolean logStreamingResponses;

    private final String apiName;
    private final String bodyModelName;
    private final Boolean logResponses;
    private final Boolean logRequests;
    private final OkHttpClient okHttpClient;
    private final Duration timeout;

    private final String apiKey;
    private final String apisecret;

    private final String authorization;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper().enable(INDENT_OUTPUT);


    private static class RequestLoggingInterceptor implements Interceptor {
        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();

            // 打印请求信息
            log.info("请求方法: {}, 请求 URL: {}, 请求头: {}", request.method(), request.url(), request.headers());
            if (request.body() != null) {
                // 创建一个 Buffer 来存储请求体内容
                Buffer buffer = new Buffer();
                request.body().writeTo(buffer);
                // 获取请求体的字符串内容
                String requestBody = buffer.readUtf8();
                log.info("请求体: {}", requestBody);
            }

            return chain.proceed(request);
        }
    }

    public TQClient(String baseUrl, TQApi unused1, boolean logStreamingResponses, String apiName,
                    String bodyModelName, Boolean logResponses, Boolean logRequests,
                    OkHttpClient unused, Duration timeout, String apiKey, String apisecret, String authorization) {

        OkHttpClient.Builder okHttpClientBuilder = new OkHttpClient.Builder()
                .callTimeout(timeout)
                .connectTimeout(timeout)
                .readTimeout(timeout)
                .writeTimeout(timeout);
        this.authorization = authorization == null ? "" : authorization;
        this.timeout = timeout;
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
        this.apisecret = apisecret;
        this.bodyModelName = bodyModelName;
        this.apiName = apiName;
        this.logResponses = logResponses;
        this.logRequests = logRequests;
        // 添加请求日志拦截器



        this.logStreamingResponses = Boolean.TRUE.equals(logStreamingResponses);


//        if (logRequests != null && logRequests) {
//            okHttpClientBuilder.addInterceptor(new OllamaRequestLoggingInterceptor());
//        }
        if (logRequests != null && logRequests) {
            okHttpClientBuilder.addInterceptor(new RequestLoggingInterceptor());
        }

        // add custom header interceptor
//        if (customHeaders != null && !customHeaders.isEmpty()) {
//            okHttpClientBuilder.addInterceptor(new OllamaClient.GenericHeadersInterceptor(customHeaders));
//        }
        OkHttpClient okHttpClient = okHttpClientBuilder.build();
        this.okHttpClient = okHttpClient;
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(Utils.ensureTrailingForwardSlash(baseUrl))
                .client(okHttpClient)
                .addConverterFactory(JacksonConverterFactory.create(OBJECT_MAPPER))
                .build();

        tqApi = retrofit.create(TQApi.class);
    }







    public ChatCompletionResponse completion(TQChatRequest request) {

        TQCompletionRequest bodeRequest = new TQCompletionRequest();
        bodeRequest.setUniBssHeadReqDto(TqApiHelper.genUniBssHeadReqVo(apiKey, apisecret));
        Map<String, TQChatRequest> body = new HashMap<>();
        body.put(bodyModelName.toUpperCase(Locale.ROOT) + "_REQ", request);
        bodeRequest.setUniBssBodyReqDto(body);

        try {
            retrofit2.Response<ChatCompletionResponse> retrofitResponse
                    = tqApi.completion(this.apiName, bodeRequest, authorization).execute();

            if (retrofitResponse.isSuccessful()) {
                log.info("response: {}", retrofitResponse.body());
                return retrofitResponse.body();
            } else {
                throw toException(retrofitResponse);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    public void streamingChat(TQChatRequest request, StreamingResponseHandler<AiMessage> handler) {
        TQCompletionRequest bodyRequest = new TQCompletionRequest();
        bodyRequest.setUniBssHeadReqDto(TqApiHelper.genUniBssHeadReqVo(apiKey, apisecret));
        Map<String, TQChatRequest> body = new HashMap<>();
        body.put(bodyModelName.toUpperCase(Locale.ROOT) + "_REQ", request);
        bodyRequest.setUniBssBodyReqDto(body);

        OpenAiStreamingResponseBuilder responseBuilder = new OpenAiStreamingResponseBuilder();
        EventSourceListener eventSourceListener = new EventSourceListener() {

            @Override
            public void onOpen(EventSource eventSource, okhttp3.Response response) {
                if (logResponses) {
                    log.debug("onOpen()");
                }
            }

            @Override
            public void onEvent(EventSource eventSource, String id, String type, String dataString) {
                if (logResponses) {
                    log.debug("onEvent() type: '{}', data: {}", type, dataString);
                }
                // log.info("onEvent() type: '{}', data: {}", type, dataString);
                if ("[DONE]".contentEquals(dataString)) {
                    handler.onComplete(responseBuilder.build());
                    return;
                }

                try {
                    ChatCompletionResponse data = OBJECT_MAPPER.readValue(dataString, ChatCompletionResponse.class);

                    responseBuilder.append(data);
                    List<ChatCompletionChoice> choices = data.choices();
                    if (choices == null || choices.isEmpty()) {
                        return;
                    }
                    if (choices.get(0).finishReason() != null &&
                            "stop".contentEquals(choices.get(0).finishReason())) {
                        handler.onComplete(responseBuilder.build());
                        return;
                    }
                    Delta delta = choices.get(0).delta();
                    String content = delta.content();
                    if (content != null) {
                        handler.onNext(content);
                    }
                } catch (Exception e) {
                    handler.onError(e);
                }
            }

            private void handleError(String dataString) {
                handler.onError(new TQHttpException(null, dataString));
            }

            @Override
            public void onFailure(EventSource eventSource, Throwable t, okhttp3.Response response) {
                if (logResponses) {
                    log.debug("onFailure()", t);
                }

                if (t != null) {
                    handler.onError(t);
                }

                if (response != null) {
                    try (ResponseBody responseBody = response.body()) {
                        if (responseBody != null) {
                            handler.onError(new TQHttpException(response.code(), responseBody.string()));
                        } else {
                            handler.onError(new TQHttpException(response.code(), null));
                        }
                    } catch (IOException e) {
                        handler.onError(new TQHttpException(response.code(), "[error reading response body]"));
                    }
                }
            }

            @Override
            public void onClosed(EventSource eventSource) {
                if (logResponses) {
                    log.debug("onClosed()");
                }
            }
        };

        Call<ChatCompletionResponse> call = tqApi.completion(this.apiName, bodyRequest, authorization);
        EventSources.createFactory(okHttpClient).newEventSource(call.request(), eventSourceListener);
    }


    private RuntimeException toException(retrofit2.Response<?> response) throws IOException {
        int code = response.code();
        String body = response.errorBody().string();

        String errorMessage = String.format("status code: %s; body: %s", code, body);
        return new RuntimeException(errorMessage);
    }


}
