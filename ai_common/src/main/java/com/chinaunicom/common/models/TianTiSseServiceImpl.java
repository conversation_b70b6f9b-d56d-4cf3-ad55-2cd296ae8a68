package com.chinaunicom.common.models;


import com.chinaunicom.common.utils.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.chinaunicom.common.models.tianti.TiantiResp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Objects;


@Slf4j
@AllArgsConstructor
public class TianTiSseServiceImpl implements SseService {

    private SseEmitter sseEmitter;


    /**
     * 发送消息
     *
     * @param sseResp
     */
    @Override
    public void sendMessage(SSEResp sseResp) {

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL);
        try {
            TiantiResp resp = new TiantiResp();
            if (Objects.requireNonNull(sseResp.getChatRespType()) == ChatRespType.error) {
                resp.setCode(300);
            } else {
                resp.setCode(200);
            }
            TiantiResp.Content data;
            if (sseResp.getChatRespType() == ChatRespType.graphUsed) {
                data = TiantiResp.Content.builder()
                        .progressType(sseResp.getChatRespType().getProgressType())
                        .progressStatus(sseResp.getChatRespSt().getStatus())
                        .conversationId(sseResp.getConversation_id())
                        .chatId(sseResp.getChat_id())
                        .chartData(sseResp.getContent())
                        .build();
            } else if (sseResp.isThinkForZhiSou()) {
                data = TiantiResp.Content.builder()
                        .progressType(sseResp.getChatRespType().getProgressType())
                        .progressStatus(sseResp.getChatRespSt().getStatus())
                        .conversationId(sseResp.getConversation_id())
                        .chatId(sseResp.getChat_id())
                        /*改为使用 think 输出*/
                        .think(sseResp.getThinkBuffer())
                        .content(sseResp.getContent())
                        .progressRate(sseResp.getProgressRate())
                        .build();
            } else {
                data = TiantiResp.Content.builder()
                        .progressType(sseResp.getChatRespType().getProgressType())
                        .progressStatus(sseResp.getChatRespSt().getStatus())
                        .conversationId(sseResp.getConversation_id())
                        .chatId(sseResp.getChat_id())
//                        .content(sseResp.getContent().contentEquals("\n") ? "<br>" : sseResp.getContent())
                        //无需做\n 替换
                        .content(sseResp.getContent())
                        //todo对进度条进行判空
                        .progressRate(sseResp.getProgressRate())
                        .build();
            }
            resp.setData(data);

            resp.setMessage(sseResp.getChatRespType().name());

            sseEmitter.send(objectMapper.writeValueAsString(resp));
        } catch (IOException ex) {
            log.error("sse 发送消息失败 {}", ex.getMessage());
        }


    }

    @Override
    public void sendFin() {
        try {
            sseEmitter.send("[DONE]");
        } catch (IOException ex) {
            log.error("sse 发送消息失败 {}", ex.getMessage());
        }
    }

    @Override
    public void close() {
        sseEmitter.complete();
    }
}

