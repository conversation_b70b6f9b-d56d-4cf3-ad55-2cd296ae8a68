package com.chinaunicom.common.entity.uniRequest;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;


@Data
public class QueryInfo {

    private String query;
    /**
     * 用户输入的意图
     */
    private String intent;
    /**
     * 多轮对话的会话id
     */
    @JsonProperty("conversation_id")
    private String conversationId;
    /**
     * 单次对话的会话id
     */
    @JsonProperty("chat_id")
    private String chatId;
    /**
     * 用户id
     */
    @JsonProperty("user_id")
    private String userId;

    /**
     * 引用 的相关信息
     * {"chat":被引用的消息的chatId "txt":若是被引用的纯文本问题, "fileName":若引用的是文件 }
     */
    private Map quote;

    /**
     * 兼容rag 知识问答
     */
    @JsonProperty("knowledge_id")
    private String knowledgeId;


    @JsonProperty("pendingId")
    private String pendingId;

    /**
     * 背景知识
     */
    @JsonProperty("backgroundInfo")
    private Map<String, Object> backgroundInfo;


    @JsonProperty("isDemandFangtan")
    private Boolean isDemandFangtan;

    @JsonProperty("toolInfo")
    private String toolInfo;


}
