package com.chinaunicom.common.flow.context;


import com.chinaunicom.common.flow.ThinkingStage;
import dev.langchain4j.data.message.SystemMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;


@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class DemandChatContext extends BaseContext {

    /*rag 查询后生成的提示词*/
    private List<SystemMessage> ragSystemMessage;


    /*思维链相关*/
    List<ThinkingStage> thinkingStage;

    List<String> answerNote;

    String chatId;

    Map<String, Object> backGroundInfo;

    String token;

    private Map<String, Object> customParams;


}
